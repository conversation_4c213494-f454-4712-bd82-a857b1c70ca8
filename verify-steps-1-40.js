#!/usr/bin/env node

/**
 * 验证开发步骤1-40的完成情况
 * 彻底检查每个步骤的实际完成状态
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

// 项目根目录
const PROJECT_ROOT = '/Volumes/Bengtai/seo/zhanqun188';
const PET_BLOG_ROOT = path.join(PROJECT_ROOT, 'pet-blog-system');
const BACKEND_ROOT = path.join(PET_BLOG_ROOT, 'backend');

// 步骤验证配置
const steps = [
    // 第一阶段：环境准备 (步骤1-10)
    {
        id: 1,
        name: '创建项目根目录',
        check: () => {
            const dirs = [
                PET_BLOG_ROOT,
                path.join(PET_BLOG_ROOT, 'backend'),
                path.join(PET_BLOG_ROOT, 'admin'),
                path.join(PET_BLOG_ROOT, 'frontend/en'),
                path.join(PET_BLOG_ROOT, 'frontend/de'),
                path.join(PET_BLOG_ROOT, 'frontend/ru')
            ];
            return dirs.every(dir => fs.existsSync(dir));
        }
    },
    {
        id: 2,
        name: '初始化Git仓库',
        check: () => {
            return fs.existsSync(path.join(PET_BLOG_ROOT, '.gitignore'));
        }
    },
    {
        id: 3,
        name: '配置本地hosts文件',
        check: () => {
            // 检查是否有hosts配置文档
            return fs.existsSync(path.join(PET_BLOG_ROOT, 'STEP3-HOSTS-CONFIG.md'));
        }
    },
    {
        id: 4,
        name: '安装Node.js和npm',
        check: () => {
            try {
                const nodeVersion = process.version;
                return nodeVersion.startsWith('v20') || nodeVersion.startsWith('v22');
            } catch {
                return false;
            }
        }
    },
    {
        id: 5,
        name: '安装MySQL客户端工具',
        check: () => {
            // 检查数据库连接测试脚本
            return fs.existsSync(path.join(BACKEND_ROOT, 'database/test-connection.js'));
        }
    },
    {
        id: 6,
        name: '创建数据库表结构',
        check: () => {
            return fs.existsSync(path.join(BACKEND_ROOT, 'database/init.sql'));
        }
    },
    {
        id: 7,
        name: '插入初始数据',
        check: () => {
            return fs.existsSync(path.join(BACKEND_ROOT, 'database/verify-data.js'));
        }
    },
    {
        id: 8,
        name: '创建后端package.json',
        check: () => {
            return fs.existsSync(path.join(BACKEND_ROOT, 'package.json'));
        }
    },
    {
        id: 9,
        name: '创建后端环境变量文件',
        check: () => {
            return fs.existsSync(path.join(BACKEND_ROOT, '.env'));
        }
    },
    {
        id: 10,
        name: '创建后端目录结构',
        check: () => {
            const dirs = [
                path.join(BACKEND_ROOT, 'src/config'),
                path.join(BACKEND_ROOT, 'src/controllers'),
                path.join(BACKEND_ROOT, 'src/models'),
                path.join(BACKEND_ROOT, 'src/routes'),
                path.join(BACKEND_ROOT, 'src/middleware'),
                path.join(BACKEND_ROOT, 'src/services'),
                path.join(BACKEND_ROOT, 'src/utils'),
                path.join(BACKEND_ROOT, 'uploads')
            ];
            return dirs.every(dir => fs.existsSync(dir));
        }
    },

    // 第二阶段：后端基础开发 (步骤11-25)
    {
        id: 11,
        name: '创建数据库连接模块',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/config/database.js'))
    },
    {
        id: 12,
        name: '创建Express应用主文件',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/index.js'))
    },
    {
        id: 13,
        name: '创建认证中间件',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/middleware/auth.js'))
    },
    {
        id: 14,
        name: '创建错误处理中间件',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/middleware/errorHandler.js'))
    },
    {
        id: 15,
        name: '创建验证中间件',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/middleware/validation.js'))
    },
    {
        id: 16,
        name: '创建CORS配置',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/middleware/cors.js'))
    },
    {
        id: 17,
        name: '创建限流中间件',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/middleware/rateLimit.js'))
    },
    {
        id: 18,
        name: '创建文章模型',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/models/article.js'))
    },
    {
        id: 19,
        name: '创建分类模型',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/models/category.js'))
    },
    {
        id: 20,
        name: '创建评论模型',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/models/comment.js'))
    },
    {
        id: 21,
        name: '创建管理员模型',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/models/admin.js'))
    },
    {
        id: 22,
        name: '创建认证控制器',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/controllers/authController.js'))
    },
    {
        id: 23,
        name: '创建认证路由',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/routes/auth.js'))
    },
    {
        id: 24,
        name: '创建响应工具函数',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/utils/response.js'))
    },
    {
        id: 25,
        name: '测试后端基础功能',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'STEP25-COMPLETE.md'))
    },

    // 第三阶段：后端核心功能开发 (步骤26-40)
    {
        id: 26,
        name: '创建文章控制器',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/controllers/articleController.js'))
    },
    {
        id: 27,
        name: '创建文章路由',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/routes/article.js'))
    },
    {
        id: 28,
        name: '创建翻译服务',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/services/translationService.js'))
    },
    {
        id: 29,
        name: '创建翻译控制器',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/controllers/translationController.js'))
    },
    {
        id: 30,
        name: '创建分类控制器',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/controllers/categoryController.js'))
    },
    {
        id: 31,
        name: '创建分类路由',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/routes/category.js'))
    },
    {
        id: 32,
        name: '创建评论控制器',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/controllers/commentController.js'))
    },
    {
        id: 33,
        name: '创建评论路由',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/routes/comment.js'))
    },
    {
        id: 34,
        name: '创建媒体上传服务',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/services/uploadService.js'))
    },
    {
        id: 35,
        name: '创建媒体控制器',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/controllers/mediaController.js'))
    },
    {
        id: 36,
        name: '创建站点配置控制器',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/controllers/configController.js'))
    },
    {
        id: 37,
        name: '创建公开API控制器',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/controllers/publicController.js'))
    },
    {
        id: 38,
        name: '创建公开API路由',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'src/routes/public.js'))
    },
    {
        id: 39,
        name: '整合所有路由',
        check: () => {
            try {
                const indexContent = fs.readFileSync(path.join(BACKEND_ROOT, 'src/index.js'), 'utf8');
                return indexContent.includes('routes/public') && 
                       indexContent.includes('routes/auth') &&
                       indexContent.includes('routes/article');
            } catch {
                return false;
            }
        }
    },
    {
        id: 40,
        name: '后端API综合测试',
        check: () => fs.existsSync(path.join(BACKEND_ROOT, 'STEP40-COMPLETE.md'))
    }
];

// 验证步骤的完成情况
async function verifySteps() {
    console.log(`${colors.cyan}====================================`);
    console.log(`  宠物博客站群系统 - 步骤验证`);
    console.log(`  验证步骤 1-40 的完成情况`);
    console.log(`====================================${colors.reset}\n`);

    const results = {
        completed: [],
        incomplete: [],
        total: steps.length
    };

    // 逐个验证步骤
    for (const step of steps) {
        try {
            const isCompleted = await step.check();
            if (isCompleted) {
                console.log(`${colors.green}✅ 步骤${step.id}${colors.reset}: ${step.name}`);
                results.completed.push(step);
            } else {
                console.log(`${colors.red}❌ 步骤${step.id}${colors.reset}: ${step.name}`);
                results.incomplete.push(step);
            }
        } catch (error) {
            console.log(`${colors.yellow}⚠️  步骤${step.id}${colors.reset}: ${step.name} (检查出错: ${error.message})`);
            results.incomplete.push(step);
        }
    }

    // 输出统计结果
    console.log(`\n${colors.cyan}====================================`);
    console.log(`  验证结果统计`);
    console.log(`====================================${colors.reset}\n`);

    const completionRate = ((results.completed.length / results.total) * 100).toFixed(1);
    
    console.log(`总步骤数: ${results.total}`);
    console.log(`${colors.green}已完成: ${results.completed.length}${colors.reset}`);
    console.log(`${colors.red}未完成: ${results.incomplete.length}${colors.reset}`);
    console.log(`完成率: ${completionRate}%\n`);

    // 按阶段统计
    const phases = [
        { name: '环境准备', start: 1, end: 10 },
        { name: '后端基础开发', start: 11, end: 25 },
        { name: '后端核心功能', start: 26, end: 40 }
    ];

    console.log(`${colors.blue}按阶段统计:${colors.reset}`);
    for (const phase of phases) {
        const phaseSteps = steps.filter(s => s.id >= phase.start && s.id <= phase.end);
        const phaseCompleted = phaseSteps.filter(s => results.completed.includes(s));
        const phaseRate = ((phaseCompleted.length / phaseSteps.length) * 100).toFixed(1);
        console.log(`  ${phase.name} (步骤${phase.start}-${phase.end}): ${phaseCompleted.length}/${phaseSteps.length} (${phaseRate}%)`);
    }

    // 未完成步骤详情
    if (results.incomplete.length > 0) {
        console.log(`\n${colors.yellow}未完成的步骤:${colors.reset}`);
        for (const step of results.incomplete) {
            console.log(`  - 步骤${step.id}: ${step.name}`);
        }
    }

    // 生成进度报告
    const report = {
        timestamp: new Date().toISOString(),
        totalSteps: results.total,
        completed: results.completed.length,
        incomplete: results.incomplete.length,
        completionRate: completionRate,
        details: steps.map(s => ({
            id: s.id,
            name: s.name,
            completed: results.completed.includes(s)
        }))
    };

    // 保存报告
    const reportPath = path.join(PROJECT_ROOT, 'verification-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\n${colors.cyan}验证报告已保存到: ${reportPath}${colors.reset}`);

    return results;
}

// 执行验证
if (require.main === module) {
    verifySteps().catch(console.error);
}

module.exports = { verifySteps };