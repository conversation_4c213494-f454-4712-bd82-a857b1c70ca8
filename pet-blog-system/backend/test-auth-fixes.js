#!/usr/bin/env node

/**
 * 测试认证系统修复
 */

const axios = require('axios');

async function testAuthSystem() {
    try {
        console.log('🔐 测试认证系统修复...\n');
        
        // 1. 测试登录
        console.log('1. 测试管理员登录');
        const loginResponse = await axios.post('http://localhost:3000/api/admin/auth/login', {
            username: 'admin',
            password: 'admin123456'
        });
        
        if (loginResponse.data.success) {
            console.log('✅ 登录成功');
            const token = loginResponse.data.data.token;
            
            // 2. 测试token验证 (/verify)
            console.log('\n2. 测试token验证 (/verify)');
            try {
                const verifyResponse = await axios.get('http://localhost:3000/api/admin/auth/verify', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                console.log('✅ /verify 端点正常工作');
            } catch (error) {
                console.log('❌ /verify 端点失败:', error.response?.data?.message || error.message);
            }
            
            // 3. 测试获取用户信息 (/profile)
            console.log('\n3. 测试获取用户信息 (/profile)');
            try {
                const profileResponse = await axios.get('http://localhost:3000/api/admin/auth/profile', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                console.log('✅ /profile 端点正常工作');
            } catch (error) {
                console.log('❌ /profile 端点失败:', error.response?.data?.message || error.message);
            }
            
            // 4. 测试原有的 /me 端点
            console.log('\n4. 测试原有的 /me 端点');
            try {
                const meResponse = await axios.get('http://localhost:3000/api/admin/auth/me', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                console.log('✅ /me 端点正常工作');
            } catch (error) {
                console.log('❌ /me 端点失败:', error.response?.data?.message || error.message);
            }
            
        } else {
            console.log('❌ 登录失败');
        }
        
        console.log('\n🔐 认证系统测试完成');
        
    } catch (error) {
        console.log('❌ 认证系统测试失败:', error.response?.data?.message || error.message);
    }
}

testAuthSystem();
