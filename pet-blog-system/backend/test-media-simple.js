/**
 * 媒体管理API简单测试脚本
 * 
 * 测试媒体上传、获取列表等基本功能
 * 
 * <AUTHOR> Blog System
 * @version 1.0.0
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

// API基础URL
const BASE_URL = 'http://localhost:3000';
const API_URL = `${BASE_URL}/api/admin`;

// 测试数据
let authToken = '';

// 测试账号
const testAdmin = {
    username: 'admin',
    password: 'admin123456'
};

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

// 输出函数
const log = {
    success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
    error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
    info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
    warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
    section: (msg) => console.log(`\n${colors.cyan}${colors.bright}📌 ${msg}${colors.reset}\n`)
};

// 创建简单的测试文本文件（模拟文档上传）
function createTestFile() {
    const content = `测试文档内容
创建时间: ${new Date().toISOString()}
这是一个用于测试上传功能的文本文件。
随机数: ${Math.random()}`;
    
    const fileName = `test-doc-${Date.now()}.txt`;
    const filePath = path.join(__dirname, fileName);
    fs.writeFileSync(filePath, content);
    
    return filePath;
}

// 登录获取token
async function login() {
    try {
        const response = await axios.post(`${API_URL}/auth/login`, testAdmin);
        authToken = response.data.data.token;
        log.success('登录成功');
        return true;
    } catch (error) {
        log.error(`登录失败: ${error.response?.data?.message || error.message}`);
        return false;
    }
}

// 测试获取媒体列表
async function testGetMediaList() {
    log.section('测试获取媒体列表');
    
    try {
        const response = await axios.get(
            `${API_URL}/media`,
            {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                },
                params: {
                    type: 'all',
                    page: 1,
                    limit: 10
                }
            }
        );
        
        if (response.data.success) {
            log.success('获取媒体列表成功');
            console.log('API响应:', {
                success: response.data.success,
                message: response.data.message,
                pagination: response.data.data.pagination,
                items_count: response.data.data.items.length
            });
        }
        
        return true;
    } catch (error) {
        log.error(`获取媒体列表失败: ${error.response?.data?.message || error.message}`);
        if (error.response?.data) {
            console.log('错误详情:', error.response.data);
        }
        return false;
    }
}

// 测试获取存储统计
async function testGetStorageStats() {
    log.section('测试获取存储统计');
    
    try {
        const response = await axios.get(
            `${API_URL}/media/stats`,
            {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            }
        );
        
        if (response.data.success) {
            log.success('获取存储统计成功');
            console.log('存储统计:', response.data.data);
        }
        
        return true;
    } catch (error) {
        log.error(`获取存储统计失败: ${error.response?.data?.message || error.message}`);
        if (error.response?.data) {
            console.log('错误详情:', error.response.data);
        }
        return false;
    }
}

// 测试清理临时文件
async function testCleanupTemp() {
    log.section('测试清理临时文件');
    
    try {
        const response = await axios.post(
            `${API_URL}/media/cleanup`,
            {},
            {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            }
        );
        
        if (response.data.success) {
            log.success('临时文件清理成功');
            console.log('响应消息:', response.data.message);
        }
        
        return true;
    } catch (error) {
        log.error(`清理临时文件失败: ${error.response?.data?.message || error.message}`);
        if (error.response?.data) {
            console.log('错误详情:', error.response.data);
        }
        return false;
    }
}

// 主测试函数
async function runTests() {
    console.log('\n' + '='.repeat(50));
    console.log(`${colors.bright}媒体管理API基础测试${colors.reset}`);
    console.log('='.repeat(50));
    
    try {
        // 登录
        if (!await login()) {
            log.error('登录失败，请确保：');
            console.log('1. 后端服务已启动 (npm run dev)');
            console.log('2. 管理员账号已创建 (node setup-admin.js)');
            return;
        }
        
        // 运行测试
        const tests = [
            testGetMediaList,
            testGetStorageStats,
            testCleanupTemp
        ];
        
        let passed = 0;
        let failed = 0;
        
        for (const test of tests) {
            const result = await test();
            if (result) {
                passed++;
            } else {
                failed++;
            }
        }
        
        // 输出测试结果
        console.log('\n' + '='.repeat(50));
        console.log(`${colors.bright}测试结果${colors.reset}`);
        console.log('='.repeat(50));
        console.log(`${colors.green}通过: ${passed}${colors.reset}`);
        console.log(`${colors.red}失败: ${failed}${colors.reset}`);
        console.log(`${colors.blue}总计: ${tests.length}${colors.reset}`);
        console.log('='.repeat(50) + '\n');
        
        if (failed === 0) {
            log.success('所有基础测试通过！媒体管理功能正常。');
            console.log('\n提示: 可以使用Postman或其他工具测试文件上传功能');
            console.log('上传端点: POST /api/admin/media/upload');
            console.log('需要在请求头中包含: Authorization: Bearer <token>');
        } else {
            log.warning(`有 ${failed} 个测试失败，请检查问题。`);
        }
        
    } catch (error) {
        log.error(`测试执行失败: ${error.message}`);
        console.error(error.stack);
    }
}

// 运行测试
runTests();