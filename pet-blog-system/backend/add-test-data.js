#!/usr/bin/env node

/**
 * 添加测试数据脚本
 */

const db = require('./src/config/database');

async function addTestData() {
    try {
        console.log('添加测试数据...\n');
        
        // 为已发布的文章添加翻译数据
        const publishedArticles = await db.query('SELECT id FROM articles WHERE status = "published" LIMIT 3');
        console.log(`找到 ${publishedArticles.length} 篇已发布文章`);
        
        for (const article of publishedArticles) {
            const articleId = article.id;
            
            // 检查是否已有翻译
            const existingTranslations = await db.query('SELECT language FROM article_translations WHERE article_id = ?', [articleId]);
            const existingLanguages = existingTranslations.map(t => t.language);
            
            const languages = ['en', 'de', 'ru'];
            
            for (const lang of languages) {
                if (!existingLanguages.includes(lang)) {
                    const title = `Pet Care Guide ${articleId} (${lang})`;
                    const content = `<p>This is a comprehensive guide about pet care. Article ${articleId} provides valuable information for pet owners.</p>`;
                    const excerpt = `Essential pet care tips and advice for article ${articleId}`;
                    const slug = `pet-care-guide-${articleId}-${lang}`;
                    const meta_title = `Pet Care Guide ${articleId} - Complete Tips`;
                    const meta_description = `Learn essential pet care tips and advice in this comprehensive guide ${articleId}`;
                    const meta_keywords = 'pet care, pets, animals, health';
                    
                    await db.query(`
                        INSERT INTO article_translations 
                        (article_id, language, title, content, excerpt, slug, meta_title, meta_description, meta_keywords, status, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'published', NOW(), NOW())
                    `, [
                        articleId, lang, title, content, excerpt, 
                        slug, meta_title, meta_description, meta_keywords
                    ]);
                    
                    console.log(`✅ 添加文章 ${articleId} 的 ${lang} 翻译`);
                } else {
                    console.log(`⏭️  文章 ${articleId} 的 ${lang} 翻译已存在`);
                }
            }
        }
        
        console.log('\n测试数据添加完成！');
        process.exit(0);
    } catch (error) {
        console.error('添加测试数据失败:', error);
        process.exit(1);
    }
}

addTestData();
