/**
 * 步骤39：综合路由测试脚本
 * 测试所有已整合的API路由
 */

const axios = require('axios');
require('dotenv').config();

const BASE_URL = 'http://localhost:3000';
let authToken = null;
let testResults = {
    total: 0,
    passed: 0,
    failed: 0,
    errors: [],
    startTime: new Date(),
    endTime: null
};

// 彩色输出
const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
};

// 测试辅助函数
async function testEndpoint(name, method, url, data = null, headers = {}) {
    testResults.total++;
    try {
        const config = {
            method,
            url: `${BASE_URL}${url}`,
            headers: {
                'Content-Type': 'application/json',
                ...headers
            }
        };

        if (data) {
            config.data = data;
        }

        const response = await axios(config);
        
        if (response.data.success !== undefined && !response.data.success) {
            throw new Error(response.data.message || 'Request failed');
        }

        console.log(`${colors.green}✓${colors.reset} ${name}`);
        testResults.passed++;
        return response.data;
    } catch (error) {
        console.log(`${colors.red}✗${colors.reset} ${name}: ${error.response?.data?.message || error.message}`);
        testResults.failed++;
        testResults.errors.push({
            test: name,
            error: error.response?.data?.message || error.message,
            status: error.response?.status
        });
        return null;
    }
}

// 主测试函数
async function runTests() {
    console.log(`${colors.cyan}${'='.repeat(60)}${colors.reset}`);
    console.log(`${colors.cyan}步骤39：综合路由测试${colors.reset}`);
    console.log(`${colors.cyan}${'='.repeat(60)}${colors.reset}\n`);

    // ====================================
    // 1. 测试健康检查和基础API
    // ====================================
    console.log(`${colors.blue}【测试健康检查和基础API】${colors.reset}`);
    
    await testEndpoint('健康检查', 'GET', '/health');
    await testEndpoint('API基础信息', 'GET', '/api');
    
    console.log();

    // ====================================
    // 2. 测试公开API路由
    // ====================================
    console.log(`${colors.blue}【测试公开API路由】${colors.reset}`);
    
    // 测试文章列表
    await testEndpoint('获取文章列表（英语）', 'GET', '/api/public/articles?language=en&page=1&limit=10');
    await testEndpoint('获取文章列表（德语）', 'GET', '/api/public/articles?language=de&page=1&limit=10');
    
    // 测试分类列表
    await testEndpoint('获取分类列表（英语）', 'GET', '/api/public/categories?language=en');
    
    // 测试站点配置
    await testEndpoint('获取站点配置（英语）', 'GET', '/api/public/site-config?language=en');
    
    // 测试热门文章
    await testEndpoint('获取热门文章', 'GET', '/api/public/articles/popular?language=en&limit=5');
    
    // 测试相关文章
    await testEndpoint('获取相关文章', 'GET', '/api/public/articles/1/related?language=en&limit=3');
    
    console.log();

    // ====================================
    // 3. 测试认证路由
    // ====================================
    console.log(`${colors.blue}【测试认证路由】${colors.reset}`);
    
    // 登录获取token
    const loginResult = await testEndpoint(
        '管理员登录',
        'POST',
        '/api/admin/auth/login',
        {
            username: 'admin',
            password: 'admin123456'
        }
    );
    
    if (loginResult && loginResult.data && loginResult.data.token) {
        authToken = loginResult.data.token;
        console.log(`${colors.green}获取到认证Token${colors.reset}`);
    } else {
        console.log(`${colors.yellow}警告：未能获取认证Token，后续管理API测试可能失败${colors.reset}`);
    }
    
    // 测试Token验证
    if (authToken) {
        await testEndpoint(
            '验证Token',
            'GET',
            '/api/admin/auth/verify',
            null,
            { Authorization: `Bearer ${authToken}` }
        );
        
        await testEndpoint(
            '获取管理员信息',
            'GET',
            '/api/admin/auth/profile',
            null,
            { Authorization: `Bearer ${authToken}` }
        );
    }
    
    console.log();

    // ====================================
    // 4. 测试管理API路由（需要认证）
    // ====================================
    if (authToken) {
        const authHeaders = { Authorization: `Bearer ${authToken}` };
        
        // 测试文章管理
        console.log(`${colors.blue}【测试文章管理API】${colors.reset}`);
        
        await testEndpoint(
            '获取文章列表（管理）',
            'GET',
            '/api/admin/articles?page=1&limit=10',
            null,
            authHeaders
        );
        
        await testEndpoint(
            '获取文章统计',
            'GET',
            '/api/admin/articles/stats',
            null,
            authHeaders
        );
        
        console.log();
        
        // 测试分类管理
        console.log(`${colors.blue}【测试分类管理API】${colors.reset}`);
        
        await testEndpoint(
            '获取分类列表（管理）',
            'GET',
            '/api/admin/categories',
            null,
            authHeaders
        );
        
        await testEndpoint(
            '获取分类树',
            'GET',
            '/api/admin/categories/tree',
            null,
            authHeaders
        );
        
        console.log();
        
        // 测试评论管理
        console.log(`${colors.blue}【测试评论管理API】${colors.reset}`);
        
        await testEndpoint(
            '获取评论列表（管理）',
            'GET',
            '/api/admin/comments?page=1&limit=10',
            null,
            authHeaders
        );
        
        await testEndpoint(
            '获取待审核评论数',
            'GET',
            '/api/admin/comments/pending/count',
            null,
            authHeaders
        );
        
        console.log();
        
        // 测试配置管理
        console.log(`${colors.blue}【测试配置管理API】${colors.reset}`);
        
        await testEndpoint(
            '获取所有配置',
            'GET',
            '/api/admin/configs',
            null,
            authHeaders
        );
        
        await testEndpoint(
            '获取英语配置',
            'GET',
            '/api/admin/configs/en',
            null,
            authHeaders
        );
        
        console.log();
        
        // 测试翻译管理
        console.log(`${colors.blue}【测试翻译管理API】${colors.reset}`);
        
        await testEndpoint(
            '获取翻译队列',
            'GET',
            '/api/admin/translations/queue?status=pending&page=1&limit=10',
            null,
            authHeaders
        );
        
        // 如果有文章，测试获取翻译状态
        await testEndpoint(
            '获取文章翻译状态',
            'GET',
            '/api/admin/articles/1/translations',
            null,
            authHeaders
        );
        
        console.log();
        
        // 测试媒体管理
        console.log(`${colors.blue}【测试媒体管理API】${colors.reset}`);
        
        await testEndpoint(
            '获取上传配置',
            'GET',
            '/api/admin/media/config',
            null,
            authHeaders
        );
        
        console.log();
    } else {
        console.log(`${colors.yellow}跳过管理API测试（需要认证）${colors.reset}\n`);
    }

    // ====================================
    // 5. 测试错误处理
    // ====================================
    console.log(`${colors.blue}【测试错误处理】${colors.reset}`);
    
    await testEndpoint('404错误处理', 'GET', '/api/non-existent-endpoint');
    await testEndpoint('无认证访问管理API', 'GET', '/api/admin/articles');
    
    if (authToken) {
        await testEndpoint(
            '无效参数错误',
            'GET',
            '/api/admin/articles/invalid-id',
            null,
            { Authorization: `Bearer ${authToken}` }
        );
    }
    
    console.log();

    // ====================================
    // 生成测试报告
    // ====================================
    testResults.endTime = new Date();
    const duration = (testResults.endTime - testResults.startTime) / 1000;
    
    console.log(`${colors.cyan}${'='.repeat(60)}${colors.reset}`);
    console.log(`${colors.cyan}测试报告${colors.reset}`);
    console.log(`${colors.cyan}${'='.repeat(60)}${colors.reset}`);
    console.log(`总测试数: ${testResults.total}`);
    console.log(`${colors.green}通过: ${testResults.passed}${colors.reset}`);
    console.log(`${colors.red}失败: ${testResults.failed}${colors.reset}`);
    console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(2)}%`);
    console.log(`执行时间: ${duration.toFixed(2)}秒`);
    
    if (testResults.errors.length > 0) {
        console.log(`\n${colors.red}失败的测试:${colors.reset}`);
        testResults.errors.forEach((err, index) => {
            console.log(`${index + 1}. ${err.test}`);
            console.log(`   错误: ${err.error}`);
            console.log(`   状态码: ${err.status || 'N/A'}`);
        });
    }
    
    // 保存测试结果到文件
    const fs = require('fs');
    fs.writeFileSync(
        'test-results-step39.json',
        JSON.stringify(testResults, null, 2)
    );
    console.log(`\n测试结果已保存到: test-results-step39.json`);
    
    // 判断测试是否全部通过
    if (testResults.failed === 0) {
        console.log(`\n${colors.green}✅ 步骤39测试全部通过！所有路由工作正常。${colors.reset}`);
    } else {
        console.log(`\n${colors.yellow}⚠️ 有${testResults.failed}个测试失败，请检查相关路由配置。${colors.reset}`);
    }
}

// 运行测试
console.log('启动路由综合测试...\n');
runTests().catch(error => {
    console.error(`${colors.red}测试执行失败:${colors.reset}`, error);
    process.exit(1);
});