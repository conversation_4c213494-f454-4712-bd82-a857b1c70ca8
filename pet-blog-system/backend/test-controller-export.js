/**
 * 测试控制器导出
 */

try {
    const TranslationController = require('./src/controllers/translationController');
    const ConfigController = require('./src/controllers/configController');
    
    console.log('TranslationController:', TranslationController);
    console.log('TranslationController.translateArticle:', TranslationController.translateArticle);
    console.log('Type of translateArticle:', typeof TranslationController.translateArticle);
    
    console.log('\nConfigController:', ConfigController);
    console.log('ConfigController.getPublicSiteConfig:', ConfigController.getPublicSiteConfig);
    console.log('Type of getPublicSiteConfig:', typeof ConfigController.getPublicSiteConfig);
    
    // 测试其他正常工作的控制器
    const ArticleController = require('./src/controllers/articleController');
    console.log('\nArticleController.createArticle:', ArticleController.createArticle);
    console.log('Type of createArticle:', typeof ArticleController.createArticle);
    
} catch (error) {
    console.error('错误:', error.message);
    console.error('堆栈:', error.stack);
}