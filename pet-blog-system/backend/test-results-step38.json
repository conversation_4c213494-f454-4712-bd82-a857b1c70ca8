{"passed": 41, "failed": 0, "total": 41, "details": [{"name": "服务器健康检查", "passed": true, "message": "服务器运行正常", "data": null, "timestamp": "2025-08-09T07:49:59.791Z"}, {"name": "获取文章列表(正确参数)", "passed": true, "message": "状态码: 200, 响应格式正确", "data": null, "timestamp": "2025-08-09T07:50:00.595Z"}, {"name": "获取文章列表(缺少语言参数)", "passed": true, "message": "预期错误: 400", "data": null, "timestamp": "2025-08-09T07:50:00.600Z"}, {"name": "获取文章列表(无效语言代码)", "passed": true, "message": "预期错误: 400", "data": null, "timestamp": "2025-08-09T07:50:00.603Z"}, {"name": "获取文章列表(带搜索)", "passed": true, "message": "状态码: 200, 响应格式正确", "data": null, "timestamp": "2025-08-09T07:50:01.331Z"}, {"name": "获取文章列表(带分类筛选)", "passed": true, "message": "状态码: 200, 响应格式正确", "data": null, "timestamp": "2025-08-09T07:50:02.099Z"}, {"name": "获取文章详情(不存在的slug)", "passed": true, "message": "预期错误: 404", "data": null, "timestamp": "2025-08-09T07:50:02.477Z"}, {"name": "获取文章详情(缺少语言参数)", "passed": true, "message": "预期错误: 400", "data": null, "timestamp": "2025-08-09T07:50:02.482Z"}, {"name": "获取文章详情(无效slug格式)", "passed": true, "message": "预期错误: 404", "data": null, "timestamp": "2025-08-09T07:50:02.707Z"}, {"name": "增加文章浏览量(不存在的文章ID)", "passed": true, "message": "预期错误: 404", "data": null, "timestamp": "2025-08-09T07:50:03.087Z"}, {"name": "增加文章浏览量(无效ID)", "passed": true, "message": "预期错误: 400", "data": null, "timestamp": "2025-08-09T07:50:03.091Z"}, {"name": "增加文章浏览量(缺少语言参数)", "passed": true, "message": "预期错误: 400", "data": null, "timestamp": "2025-08-09T07:50:03.093Z"}, {"name": "获取分类列表(正确参数)", "passed": true, "message": "状态码: 200, 响应格式正确", "data": null, "timestamp": "2025-08-09T07:50:03.483Z"}, {"name": "获取分类列表(缺少语言参数)", "passed": true, "message": "预期错误: 400", "data": null, "timestamp": "2025-08-09T07:50:03.486Z"}, {"name": "获取分类列表(带类型筛选)", "passed": true, "message": "状态码: 200, 响应格式正确", "data": null, "timestamp": "2025-08-09T07:50:03.846Z"}, {"name": "获取分类列表(无效类型)", "passed": true, "message": "预期错误: 400", "data": null, "timestamp": "2025-08-09T07:50:03.849Z"}, {"name": "获取评论列表(不存在的文章ID)", "passed": true, "message": "预期错误: 404", "data": null, "timestamp": "2025-08-09T07:50:04.027Z"}, {"name": "获取评论列表(缺少文章ID)", "passed": true, "message": "预期错误: 400", "data": null, "timestamp": "2025-08-09T07:50:04.030Z"}, {"name": "获取评论列表(无效文章ID)", "passed": true, "message": "预期错误: 400", "data": null, "timestamp": "2025-08-09T07:50:04.032Z"}, {"name": "提交评论(不存在的文章ID)", "passed": true, "message": "预期错误: 404", "data": null, "timestamp": "2025-08-09T07:50:04.623Z"}, {"name": "提交评论(缺少必需参数)", "passed": true, "message": "预期错误: 400", "data": null, "timestamp": "2025-08-09T07:50:04.628Z"}, {"name": "提交评论(无效邮箱)", "passed": true, "message": "预期错误: 400", "data": null, "timestamp": "2025-08-09T07:50:04.632Z"}, {"name": "提交评论(内容过短)", "passed": true, "message": "预期错误: 400", "data": null, "timestamp": "2025-08-09T07:50:04.634Z"}, {"name": "提交评论(用户名过短)", "passed": true, "message": "预期错误: 400", "data": null, "timestamp": "2025-08-09T07:50:04.637Z"}, {"name": "提交回复评论(不存在的文章ID)", "passed": true, "message": "预期错误: 404", "data": null, "timestamp": "2025-08-09T07:50:05.015Z"}, {"name": "搜索文章(正确参数)", "passed": true, "message": "状态码: 200, 响应格式正确", "data": null, "timestamp": "2025-08-09T07:50:05.757Z"}, {"name": "搜索分类", "passed": true, "message": "状态码: 200, 响应格式正确", "data": null, "timestamp": "2025-08-09T07:50:06.161Z"}, {"name": "搜索(缺少关键词)", "passed": true, "message": "预期错误: 400", "data": null, "timestamp": "2025-08-09T07:50:06.164Z"}, {"name": "搜索(空关键词)", "passed": true, "message": "预期错误: 400", "data": null, "timestamp": "2025-08-09T07:50:06.167Z"}, {"name": "搜索(无效类型)", "passed": true, "message": "预期错误: 400", "data": null, "timestamp": "2025-08-09T07:50:06.169Z"}, {"name": "搜索(关键词太长)", "passed": true, "message": "预期错误: 400", "data": null, "timestamp": "2025-08-09T07:50:06.171Z"}, {"name": "获取站点配置(正确参数)", "passed": true, "message": "状态码: 200, 响应格式正确", "data": null, "timestamp": "2025-08-09T07:50:06.535Z"}, {"name": "获取站点配置(缺少语言参数)", "passed": true, "message": "预期错误: 400", "data": null, "timestamp": "2025-08-09T07:50:06.537Z"}, {"name": "获取站点配置(无效语言代码)", "passed": true, "message": "预期错误: 400", "data": null, "timestamp": "2025-08-09T07:50:06.539Z"}, {"name": "获取路由状态", "passed": true, "message": "状态码: 200, 响应格式正确", "data": null, "timestamp": "2025-08-09T07:50:06.541Z"}, {"name": "验证测试端点", "passed": true, "message": "状态码: 200, 响应格式正确", "data": null, "timestamp": "2025-08-09T07:50:06.543Z"}, {"name": "获取限流信息", "passed": true, "message": "状态码: 200, 响应格式正确", "data": null, "timestamp": "2025-08-09T07:50:06.544Z"}, {"name": "不存在的端点", "passed": true, "message": "返回状态: 404", "data": null, "timestamp": "2025-08-09T07:50:06.547Z"}, {"name": "无效HTTP方法", "passed": true, "message": "返回状态: 404", "data": null, "timestamp": "2025-08-09T07:50:06.548Z"}, {"name": "大量数据请求(limit=50)", "passed": true, "message": "状态码: 200, 响应格式正确", "data": null, "timestamp": "2025-08-09T07:50:07.227Z"}, {"name": "超出限制的请求(limit=100)", "passed": true, "message": "预期错误: 400", "data": null, "timestamp": "2025-08-09T07:50:07.231Z"}]}