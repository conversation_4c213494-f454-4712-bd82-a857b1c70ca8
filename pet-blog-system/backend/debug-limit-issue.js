/**
 * 调试LIMIT参数问题
 */

const { query } = require('./src/config/database');

async function debugLimitIssue() {
    console.log('🔍 调试MySQL LIMIT参数问题');
    
    try {
        // 测试1: 基本查询不带LIMIT
        console.log('\n测试1: 基本查询');
        const basic = await query(`
            SELECT COUNT(*) as count
            FROM articles a 
            INNER JOIN article_translations t ON a.id = t.article_id 
            WHERE t.status = "published" AND t.language = ?
        `, ['en']);
        console.log('基本查询结果:', basic);
        
        // 测试2: 硬编码LIMIT
        console.log('\n测试2: 硬编码LIMIT');
        const hardcoded = await query(`
            SELECT t.id as translation_id, a.id, t.title
            FROM articles a 
            INNER JOIN article_translations t ON a.id = t.article_id 
            WHERE t.status = "published" AND t.language = ?
            ORDER BY t.published_at DESC 
            LIMIT 5
        `, ['en']);
        console.log('硬编码LIMIT结果:', hardcoded.length, '条记录');
        
        // 测试3: 参数化LIMIT（原问题）
        console.log('\n测试3: 参数化LIMIT');
        try {
            const parameterized = await query(`
                SELECT t.id as translation_id, a.id, t.title
                FROM articles a 
                INNER JOIN article_translations t ON a.id = t.article_id 
                WHERE t.status = "published" AND t.language = ?
                ORDER BY t.published_at DESC 
                LIMIT ?, ?
            `, ['en', 0, 5]);
            console.log('参数化LIMIT结果:', parameterized.length, '条记录');
        } catch (error) {
            console.error('参数化LIMIT失败:', error.message);
        }
        
        // 测试4: 字符串拼接LIMIT
        console.log('\n测试4: 字符串拼接LIMIT');
        const offset = 0;
        const limit = 5;
        const stringConcat = await query(`
            SELECT t.id as translation_id, a.id, t.title
            FROM articles a 
            INNER JOIN article_translations t ON a.id = t.article_id 
            WHERE t.status = "published" AND t.language = ?
            ORDER BY t.published_at DESC 
            LIMIT ${offset}, ${limit}
        `, ['en']);
        console.log('字符串拼接LIMIT结果:', stringConcat.length, '条记录');
        
    } catch (error) {
        console.error('调试过程中发生错误:', error);
    }
    
    console.log('\n✅ 调试完成');
    process.exit(0);
}

debugLimitIssue();