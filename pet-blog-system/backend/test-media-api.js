/**
 * 媒体管理API测试脚本
 * 
 * 测试媒体上传、获取列表、删除等功能
 * 
 * <AUTHOR> Blog System
 * @version 1.0.0
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

// API基础URL
const BASE_URL = 'http://localhost:3000';
const API_URL = `${BASE_URL}/api/admin`;

// 测试数据
let authToken = '';
let uploadedFiles = [];

// 测试账号（需要先运行 setup-admin.js 创建管理员）
const testAdmin = {
    username: 'admin',
    password: 'admin123456'
};

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

// 输出函数
const log = {
    success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
    error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
    info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
    warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
    section: (msg) => console.log(`\n${colors.cyan}${colors.bright}📌 ${msg}${colors.reset}\n`)
};

// 创建测试图片
async function createTestImage() {
    const { createCanvas } = require('canvas');
    const canvas = createCanvas(800, 600);
    const ctx = canvas.getContext('2d');
    
    // 绘制渐变背景
    const gradient = ctx.createLinearGradient(0, 0, 800, 600);
    gradient.addColorStop(0, '#FF6B6B');
    gradient.addColorStop(1, '#4ECDC4');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 800, 600);
    
    // 添加文字
    ctx.fillStyle = 'white';
    ctx.font = 'bold 48px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Test Image', 400, 300);
    ctx.font = '24px Arial';
    ctx.fillText(new Date().toISOString(), 400, 350);
    
    // 保存为文件
    const buffer = canvas.toBuffer('image/jpeg');
    const fileName = `test-image-${Date.now()}.jpg`;
    const filePath = path.join(__dirname, fileName);
    fs.writeFileSync(filePath, buffer);
    
    return filePath;
}

// 创建测试文档
async function createTestDocument() {
    const content = `测试文档内容
创建时间: ${new Date().toISOString()}
这是一个用于测试文档上传功能的PDF文件。`;
    
    const fileName = `test-doc-${Date.now()}.txt`;
    const filePath = path.join(__dirname, fileName);
    fs.writeFileSync(filePath, content);
    
    return filePath;
}

// 登录获取token
async function login() {
    try {
        const response = await axios.post(`${API_URL}/auth/login`, testAdmin);
        authToken = response.data.data.token;
        log.success('登录成功');
        return true;
    } catch (error) {
        log.error(`登录失败: ${error.response?.data?.message || error.message}`);
        return false;
    }
}

// 测试上传单个图片
async function testUploadImage() {
    log.section('测试上传单个图片');
    
    try {
        // 创建测试图片
        const imagePath = await createTestImage();
        log.info(`创建测试图片: ${path.basename(imagePath)}`);
        
        // 准备表单数据
        const form = new FormData();
        form.append('file', fs.createReadStream(imagePath));
        form.append('type', 'article');
        form.append('alt_text', '测试图片描述');
        
        // 发送请求
        const response = await axios.post(
            `${API_URL}/media/upload`,
            form,
            {
                headers: {
                    ...form.getHeaders(),
                    'Authorization': `Bearer ${authToken}`
                }
            }
        );
        
        if (response.data.success) {
            log.success('图片上传成功');
            console.log('返回数据:', JSON.stringify(response.data.data, null, 2));
            uploadedFiles.push(response.data.data.images.original.path);
            
            // 清理测试文件
            fs.unlinkSync(imagePath);
        }
        
        return true;
    } catch (error) {
        log.error(`图片上传失败: ${error.response?.data?.message || error.message}`);
        return false;
    }
}

// 测试批量上传图片
async function testUploadMultipleImages() {
    log.section('测试批量上传图片');
    
    try {
        // 创建多个测试图片
        const imagePaths = [];
        for (let i = 0; i < 3; i++) {
            const imagePath = await createTestImage();
            imagePaths.push(imagePath);
            log.info(`创建测试图片 ${i + 1}: ${path.basename(imagePath)}`);
        }
        
        // 准备表单数据
        const form = new FormData();
        imagePaths.forEach(imagePath => {
            form.append('files', fs.createReadStream(imagePath));
        });
        form.append('type', 'cover');
        
        // 发送请求
        const response = await axios.post(
            `${API_URL}/media/upload-multiple`,
            form,
            {
                headers: {
                    ...form.getHeaders(),
                    'Authorization': `Bearer ${authToken}`
                }
            }
        );
        
        if (response.data.success) {
            log.success(`批量上传成功: ${response.data.message}`);
            console.log('上传统计:', {
                total: response.data.data.total,
                success: response.data.data.success_count,
                failed: response.data.data.failed_count
            });
            
            // 记录上传的文件
            response.data.data.uploaded.forEach(file => {
                uploadedFiles.push(file.images.original.path);
            });
            
            // 清理测试文件
            imagePaths.forEach(imagePath => fs.unlinkSync(imagePath));
        }
        
        return true;
    } catch (error) {
        log.error(`批量上传失败: ${error.response?.data?.message || error.message}`);
        return false;
    }
}

// 测试获取媒体列表
async function testGetMediaList() {
    log.section('测试获取媒体列表');
    
    try {
        const response = await axios.get(
            `${API_URL}/media`,
            {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                },
                params: {
                    type: 'all',
                    page: 1,
                    limit: 10
                }
            }
        );
        
        if (response.data.success) {
            log.success('获取媒体列表成功');
            console.log('分页信息:', response.data.data.pagination);
            console.log('文件数量:', response.data.data.items.length);
            
            if (response.data.data.items.length > 0) {
                console.log('第一个文件:', response.data.data.items[0]);
            }
        }
        
        return true;
    } catch (error) {
        log.error(`获取媒体列表失败: ${error.response?.data?.message || error.message}`);
        return false;
    }
}

// 测试获取存储统计
async function testGetStorageStats() {
    log.section('测试获取存储统计');
    
    try {
        const response = await axios.get(
            `${API_URL}/media/stats`,
            {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            }
        );
        
        if (response.data.success) {
            log.success('获取存储统计成功');
            console.log('存储统计:', JSON.stringify(response.data.data, null, 2));
        }
        
        return true;
    } catch (error) {
        log.error(`获取存储统计失败: ${error.response?.data?.message || error.message}`);
        return false;
    }
}

// 测试删除文件
async function testDeleteMedia() {
    log.section('测试删除文件');
    
    if (uploadedFiles.length === 0) {
        log.warning('没有可删除的文件，跳过测试');
        return true;
    }
    
    try {
        const fileToDelete = uploadedFiles[0];
        log.info(`删除文件: ${fileToDelete}`);
        
        const response = await axios.delete(
            `${API_URL}/media`,
            {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                },
                data: {
                    path: fileToDelete
                }
            }
        );
        
        if (response.data.success) {
            log.success('文件删除成功');
            uploadedFiles = uploadedFiles.filter(f => f !== fileToDelete);
        }
        
        return true;
    } catch (error) {
        log.error(`删除文件失败: ${error.response?.data?.message || error.message}`);
        return false;
    }
}

// 测试批量删除文件
async function testDeleteMultipleMedia() {
    log.section('测试批量删除文件');
    
    if (uploadedFiles.length < 2) {
        log.warning('文件数量不足，跳过批量删除测试');
        return true;
    }
    
    try {
        const filesToDelete = uploadedFiles.slice(0, 2);
        log.info(`批量删除 ${filesToDelete.length} 个文件`);
        
        const response = await axios.delete(
            `${API_URL}/media/batch`,
            {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                },
                data: {
                    paths: filesToDelete
                }
            }
        );
        
        if (response.data.success) {
            log.success(`批量删除成功: ${response.data.message}`);
            console.log('删除统计:', {
                total: response.data.data.total,
                success: response.data.data.success_count,
                failed: response.data.data.failed_count
            });
            
            uploadedFiles = uploadedFiles.filter(f => !filesToDelete.includes(f));
        }
        
        return true;
    } catch (error) {
        log.error(`批量删除失败: ${error.response?.data?.message || error.message}`);
        return false;
    }
}

// 测试清理临时文件
async function testCleanupTemp() {
    log.section('测试清理临时文件');
    
    try {
        const response = await axios.post(
            `${API_URL}/media/cleanup`,
            {},
            {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            }
        );
        
        if (response.data.success) {
            log.success('临时文件清理成功');
        }
        
        return true;
    } catch (error) {
        log.error(`清理临时文件失败: ${error.response?.data?.message || error.message}`);
        return false;
    }
}

// 清理剩余的测试文件
async function cleanup() {
    log.section('清理测试文件');
    
    if (uploadedFiles.length > 0) {
        try {
            const response = await axios.delete(
                `${API_URL}/media/batch`,
                {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    },
                    data: {
                        paths: uploadedFiles
                    }
                }
            );
            
            if (response.data.success) {
                log.success(`清理了 ${response.data.data.success_count} 个测试文件`);
            }
        } catch (error) {
            log.warning(`清理测试文件失败: ${error.response?.data?.message || error.message}`);
        }
    }
}

// 主测试函数
async function runTests() {
    console.log('\n' + '='.repeat(50));
    console.log(`${colors.bright}媒体管理API测试${colors.reset}`);
    console.log('='.repeat(50));
    
    try {
        // 检查是否安装了canvas（用于创建测试图片）
        try {
            require('canvas');
        } catch (error) {
            log.warning('未安装canvas模块，正在安装...');
            const { execSync } = require('child_process');
            execSync('npm install canvas', { stdio: 'inherit' });
            log.success('canvas模块安装完成');
        }
        
        // 登录
        if (!await login()) {
            log.error('登录失败，请确保管理员账号已创建（运行 node setup-admin.js）');
            return;
        }
        
        // 运行测试
        const tests = [
            testUploadImage,
            testUploadMultipleImages,
            testGetMediaList,
            testGetStorageStats,
            testDeleteMedia,
            testDeleteMultipleMedia,
            testCleanupTemp
        ];
        
        let passed = 0;
        let failed = 0;
        
        for (const test of tests) {
            const result = await test();
            if (result) {
                passed++;
            } else {
                failed++;
            }
        }
        
        // 清理测试文件
        await cleanup();
        
        // 输出测试结果
        console.log('\n' + '='.repeat(50));
        console.log(`${colors.bright}测试结果${colors.reset}`);
        console.log('='.repeat(50));
        console.log(`${colors.green}通过: ${passed}${colors.reset}`);
        console.log(`${colors.red}失败: ${failed}${colors.reset}`);
        console.log(`${colors.blue}总计: ${tests.length}${colors.reset}`);
        console.log('='.repeat(50) + '\n');
        
        if (failed === 0) {
            log.success('所有测试通过！媒体管理功能正常。');
        } else {
            log.warning(`有 ${failed} 个测试失败，请检查问题。`);
        }
        
    } catch (error) {
        log.error(`测试执行失败: ${error.message}`);
        console.error(error.stack);
    }
}

// 运行测试
runTests();