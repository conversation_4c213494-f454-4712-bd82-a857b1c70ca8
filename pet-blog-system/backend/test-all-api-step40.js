/**
 * 步骤40: 后端API综合测试
 * 
 * 测试所有已开发的API端点（步骤26-39）
 * 包括：认证、文章、分类、评论、翻译、配置、媒体管理等所有功能
 * 
 * 运行方式：node test-all-api-step40.js
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

// 基础配置
const BASE_URL = 'http://localhost:3000';
const API_URL = `${BASE_URL}/api`;
let authToken = null;

// 测试结果收集
const testResults = {
    total: 0,
    passed: 0,
    failed: 0,
    errors: [],
    startTime: new Date(),
    sections: {}
};

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

// 测试工具函数
async function runTest(name, testFunc) {
    testResults.total++;
    try {
        await testFunc();
        testResults.passed++;
        console.log(`${colors.green}✅ ${name}${colors.reset}`);
        return true;
    } catch (error) {
        testResults.failed++;
        const errorInfo = {
            test: name,
            error: error.response?.data?.message || error.message,
            status: error.response?.status,
            details: error.response?.data
        };
        testResults.errors.push(errorInfo);
        console.log(`${colors.red}❌ ${name}${colors.reset}`);
        console.log(`   错误: ${errorInfo.error}`);
        return false;
    }
}

// 记录测试章节
function startSection(name) {
    console.log(`\n${colors.cyan}${'='.repeat(60)}${colors.reset}`);
    console.log(`${colors.cyan}测试章节: ${name}${colors.reset}`);
    console.log(`${colors.cyan}${'='.repeat(60)}${colors.reset}\n`);
    testResults.sections[name] = {
        total: 0,
        passed: 0,
        failed: 0,
        startTime: new Date()
    };
    return name;
}

function endSection(name) {
    const section = testResults.sections[name];
    if (section) {
        section.endTime = new Date();
        section.duration = section.endTime - section.startTime;
        console.log(`\n${colors.blue}章节小结: ${name}${colors.reset}`);
        console.log(`通过: ${section.passed}/${section.total} | 失败: ${section.failed}`);
    }
}

// 更新章节统计
function updateSection(sectionName, passed) {
    const section = testResults.sections[sectionName];
    if (section) {
        section.total++;
        if (passed) {
            section.passed++;
        } else {
            section.failed++;
        }
    }
}

// ====================================
// 测试套件
// ====================================

/**
 * 1. 健康检查和基础API测试
 */
async function testHealthAndBasics() {
    const section = startSection('健康检查和基础API');
    
    let passed = await runTest('健康检查端点', async () => {
        const response = await axios.get(`${BASE_URL}/health`);
        if (!response.data.success || response.data.data.status !== 'healthy') {
            throw new Error('健康检查失败');
        }
    });
    updateSection(section, passed);
    
    passed = await runTest('API基础信息', async () => {
        const response = await axios.get(`${API_URL}`);
        if (!response.data.success || !response.data.data.version) {
            throw new Error('API信息获取失败');
        }
    });
    updateSection(section, passed);
    
    endSection(section);
}

/**
 * 2. 认证系统测试
 */
async function testAuthentication() {
    const section = startSection('认证系统');
    
    // 登录测试
    let passed = await runTest('管理员登录', async () => {
        const response = await axios.post(`${API_URL}/admin/auth/login`, {
            username: 'admin',
            password: 'admin123'
        });
        if (!response.data.success || !response.data.data.token) {
            throw new Error('登录失败');
        }
        authToken = response.data.data.token;
    });
    updateSection(section, passed);
    
    // Token验证
    passed = await runTest('Token验证', async () => {
        const response = await axios.get(`${API_URL}/admin/auth/verify`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        if (!response.data.success) {
            throw new Error('Token验证失败');
        }
    });
    updateSection(section, passed);
    
    // 获取管理员信息
    passed = await runTest('获取管理员信息', async () => {
        const response = await axios.get(`${API_URL}/admin/auth/profile`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        if (!response.data.success || !response.data.data.username) {
            throw new Error('获取管理员信息失败');
        }
    });
    updateSection(section, passed);
    
    // 无认证访问保护接口
    passed = await runTest('无认证访问管理API（应该失败）', async () => {
        try {
            await axios.get(`${API_URL}/admin/articles`);
            throw new Error('应该返回401错误');
        } catch (error) {
            if (error.response?.status !== 401) {
                throw new Error('应该返回401状态码');
            }
        }
    });
    updateSection(section, passed);
    
    endSection(section);
}

/**
 * 3. 分类管理测试
 */
async function testCategories() {
    const section = startSection('分类管理');
    let createdCategoryId = null;
    
    // 获取所有分类
    let passed = await runTest('获取所有分类', async () => {
        const response = await axios.get(`${API_URL}/admin/categories`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        if (!response.data.success || !Array.isArray(response.data.data.categories)) {
            throw new Error('获取分类列表失败');
        }
    });
    updateSection(section, passed);
    
    // 创建分类
    passed = await runTest('创建新分类', async () => {
        const response = await axios.post(`${API_URL}/admin/categories`, {
            name: '测试分类',
            slug: 'test-category-' + Date.now(),
            pet_type: 'cat',
            parent_id: null,
            description: '这是测试分类',
            sort_order: 100
        }, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        if (!response.data.success || !response.data.data.id) {
            throw new Error('创建分类失败');
        }
        createdCategoryId = response.data.data.id;
    });
    updateSection(section, passed);
    
    // 更新分类
    if (createdCategoryId) {
        passed = await runTest('更新分类', async () => {
            const response = await axios.put(`${API_URL}/admin/categories/${createdCategoryId}`, {
                name: '更新的测试分类',
                description: '更新的描述'
            }, {
                headers: { Authorization: `Bearer ${authToken}` }
            });
            if (!response.data.success) {
                throw new Error('更新分类失败');
            }
        });
        updateSection(section, passed);
    }
    
    // 获取分类详情
    if (createdCategoryId) {
        passed = await runTest('获取分类详情', async () => {
            const response = await axios.get(`${API_URL}/admin/categories/${createdCategoryId}`, {
                headers: { Authorization: `Bearer ${authToken}` }
            });
            if (!response.data.success || !response.data.data.id) {
                throw new Error('获取分类详情失败');
            }
        });
        updateSection(section, passed);
    }
    
    // 公开API - 获取分类列表
    passed = await runTest('公开API - 获取分类列表', async () => {
        const response = await axios.get(`${API_URL}/public/categories?language=zh`);
        if (!response.data.success || !Array.isArray(response.data.data.categories)) {
            throw new Error('公开API获取分类失败');
        }
    });
    updateSection(section, passed);
    
    // 删除分类
    if (createdCategoryId) {
        passed = await runTest('删除分类', async () => {
            const response = await axios.delete(`${API_URL}/admin/categories/${createdCategoryId}`, {
                headers: { Authorization: `Bearer ${authToken}` }
            });
            if (!response.data.success) {
                throw new Error('删除分类失败');
            }
        });
        updateSection(section, passed);
    }
    
    endSection(section);
}

/**
 * 4. 文章管理测试
 */
async function testArticles() {
    const section = startSection('文章管理');
    let createdArticleId = null;
    
    // 获取文章列表
    let passed = await runTest('获取文章列表', async () => {
        const response = await axios.get(`${API_URL}/admin/articles`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        if (!response.data.success || !response.data.data) {
            throw new Error('获取文章列表失败');
        }
    });
    updateSection(section, passed);
    
    // 创建文章
    passed = await runTest('创建新文章', async () => {
        const response = await axios.post(`${API_URL}/admin/articles`, {
            title: '测试文章标题',
            content: '这是测试文章的内容',
            excerpt: '文章摘要',
            slug: 'test-article-' + Date.now(),
            category_id: 1,
            tags: '测试,标签',
            meta_title: 'SEO标题',
            meta_description: 'SEO描述',
            meta_keywords: 'SEO关键词',
            status: 'draft',
            language: 'zh'
        }, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        if (!response.data.success || !response.data.data.id) {
            throw new Error('创建文章失败');
        }
        createdArticleId = response.data.data.id;
    });
    updateSection(section, passed);
    
    // 更新文章
    if (createdArticleId) {
        passed = await runTest('更新文章', async () => {
            const response = await axios.put(`${API_URL}/admin/articles/${createdArticleId}`, {
                title: '更新的测试文章标题',
                content: '更新的文章内容',
                status: 'published'
            }, {
                headers: { Authorization: `Bearer ${authToken}` }
            });
            if (!response.data.success) {
                throw new Error('更新文章失败');
            }
        });
        updateSection(section, passed);
    }
    
    // 获取文章详情
    if (createdArticleId) {
        passed = await runTest('获取文章详情', async () => {
            const response = await axios.get(`${API_URL}/admin/articles/${createdArticleId}`, {
                headers: { Authorization: `Bearer ${authToken}` }
            });
            if (!response.data.success || !response.data.data.id) {
                throw new Error('获取文章详情失败');
            }
        });
        updateSection(section, passed);
    }
    
    // 公开API - 获取文章列表
    passed = await runTest('公开API - 获取文章列表', async () => {
        const response = await axios.get(`${API_URL}/public/articles?language=zh`);
        if (!response.data.success) {
            throw new Error('公开API获取文章列表失败');
        }
    });
    updateSection(section, passed);
    
    // 搜索文章
    passed = await runTest('搜索文章', async () => {
        const response = await axios.get(`${API_URL}/public/articles/search?language=zh&keyword=测试`);
        if (!response.data.success) {
            throw new Error('搜索文章失败');
        }
    });
    updateSection(section, passed);
    
    // 删除文章
    if (createdArticleId) {
        passed = await runTest('删除文章', async () => {
            const response = await axios.delete(`${API_URL}/admin/articles/${createdArticleId}`, {
                headers: { Authorization: `Bearer ${authToken}` }
            });
            if (!response.data.success) {
                throw new Error('删除文章失败');
            }
        });
        updateSection(section, passed);
    }
    
    endSection(section);
}

/**
 * 5. 评论管理测试
 */
async function testComments() {
    const section = startSection('评论管理');
    let createdCommentId = null;
    
    // 获取评论列表
    let passed = await runTest('获取评论列表', async () => {
        const response = await axios.get(`${API_URL}/admin/comments`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        if (!response.data.success || !response.data.data) {
            throw new Error('获取评论列表失败');
        }
    });
    updateSection(section, passed);
    
    // 提交评论（公开API）
    passed = await runTest('提交评论', async () => {
        const response = await axios.post(`${API_URL}/public/comments`, {
            article_id: 1,
            author: '测试用户',
            email: '<EMAIL>',
            content: '这是一条测试评论',
            language: 'zh'
        });
        if (!response.data.success || !response.data.data.id) {
            throw new Error('提交评论失败');
        }
        createdCommentId = response.data.data.id;
    });
    updateSection(section, passed);
    
    // 审核评论
    if (createdCommentId) {
        passed = await runTest('审核评论', async () => {
            const response = await axios.put(`${API_URL}/admin/comments/${createdCommentId}/approve`, {}, {
                headers: { Authorization: `Bearer ${authToken}` }
            });
            if (!response.data.success) {
                throw new Error('审核评论失败');
            }
        });
        updateSection(section, passed);
    }
    
    // 获取文章评论（公开API）
    passed = await runTest('获取文章评论', async () => {
        const response = await axios.get(`${API_URL}/public/articles/1/comments?language=zh`);
        if (!response.data.success) {
            throw new Error('获取文章评论失败');
        }
    });
    updateSection(section, passed);
    
    // 获取待审核评论数
    passed = await runTest('获取待审核评论数', async () => {
        const response = await axios.get(`${API_URL}/admin/comments/pending-count`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        if (!response.data.success || typeof response.data.data.count !== 'number') {
            throw new Error('获取待审核评论数失败');
        }
    });
    updateSection(section, passed);
    
    // 删除评论
    if (createdCommentId) {
        passed = await runTest('删除评论', async () => {
            const response = await axios.delete(`${API_URL}/admin/comments/${createdCommentId}`, {
                headers: { Authorization: `Bearer ${authToken}` }
            });
            if (!response.data.success) {
                throw new Error('删除评论失败');
            }
        });
        updateSection(section, passed);
    }
    
    endSection(section);
}

/**
 * 6. 翻译功能测试
 */
async function testTranslation() {
    const section = startSection('翻译功能');
    
    // 获取翻译配置
    let passed = await runTest('获取翻译配置', async () => {
        const response = await axios.get(`${API_URL}/admin/translation-config`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        if (!response.data.success) {
            throw new Error('获取翻译配置失败');
        }
    });
    updateSection(section, passed);
    
    // 注意：实际翻译需要有效的文章ID和API配置
    // 这里只测试接口是否正确响应
    passed = await runTest('翻译文章（模拟）', async () => {
        try {
            await axios.post(`${API_URL}/admin/articles/1/translate`, {
                targetLanguages: ['en']
            }, {
                headers: { Authorization: `Bearer ${authToken}` }
            });
        } catch (error) {
            // 预期可能失败（如果文章不存在或API未配置）
            if (error.response?.status === 404 || error.response?.status === 400) {
                // 这是预期的错误，接口正常工作
                return;
            }
            throw error;
        }
    });
    updateSection(section, passed);
    
    endSection(section);
}

/**
 * 7. 配置管理测试
 */
async function testConfiguration() {
    const section = startSection('配置管理');
    
    // 获取所有配置
    let passed = await runTest('获取所有站点配置', async () => {
        const response = await axios.get(`${API_URL}/admin/configs`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        if (!response.data.success || !Array.isArray(response.data.data)) {
            throw new Error('获取站点配置失败');
        }
    });
    updateSection(section, passed);
    
    // 获取公开站点配置
    passed = await runTest('获取公开站点配置', async () => {
        const response = await axios.get(`${API_URL}/public/site-config?language=zh`);
        if (!response.data.success) {
            throw new Error('获取公开站点配置失败');
        }
    });
    updateSection(section, passed);
    
    // 获取API配置
    passed = await runTest('获取API配置', async () => {
        const response = await axios.get(`${API_URL}/admin/api-configs`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        if (!response.data.success || !Array.isArray(response.data.data)) {
            throw new Error('获取API配置失败');
        }
    });
    updateSection(section, passed);
    
    // 获取激活的API配置
    passed = await runTest('获取激活的API配置', async () => {
        const response = await axios.get(`${API_URL}/admin/api-configs/active`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        if (!response.data.success) {
            throw new Error('获取激活的API配置失败');
        }
    });
    updateSection(section, passed);
    
    endSection(section);
}

/**
 * 8. 媒体管理测试
 */
async function testMedia() {
    const section = startSection('媒体管理');
    
    // 获取上传配置
    let passed = await runTest('获取上传配置', async () => {
        const response = await axios.get(`${API_URL}/admin/media/upload-config`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        if (!response.data.success || !response.data.data.maxFileSize) {
            throw new Error('获取上传配置失败');
        }
    });
    updateSection(section, passed);
    
    // 获取媒体列表
    passed = await runTest('获取媒体列表', async () => {
        const response = await axios.get(`${API_URL}/admin/media`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        if (!response.data.success || !response.data.data) {
            throw new Error('获取媒体列表失败');
        }
    });
    updateSection(section, passed);
    
    // 获取存储统计
    passed = await runTest('获取存储统计', async () => {
        const response = await axios.get(`${API_URL}/admin/media/storage-stats`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        if (!response.data.success || !response.data.data.totalSize !== undefined) {
            throw new Error('获取存储统计失败');
        }
    });
    updateSection(section, passed);
    
    // 注意：实际上传测试需要真实文件
    // 这里只测试接口是否正确响应
    
    endSection(section);
}

/**
 * 9. 公开API测试
 */
async function testPublicAPIs() {
    const section = startSection('公开API');
    
    // 获取热门文章
    let passed = await runTest('获取热门文章', async () => {
        const response = await axios.get(`${API_URL}/public/articles/popular?language=zh`);
        if (!response.data.success) {
            throw new Error('获取热门文章失败');
        }
    });
    updateSection(section, passed);
    
    // 获取相关文章
    passed = await runTest('获取相关文章', async () => {
        const response = await axios.get(`${API_URL}/public/articles/1/related?language=zh`);
        // 如果文章不存在，可能返回404，这是正常的
        if (!response.data.success && response.status !== 404) {
            throw new Error('获取相关文章失败');
        }
    });
    updateSection(section, passed);
    
    // 获取标签
    passed = await runTest('获取热门标签', async () => {
        const response = await axios.get(`${API_URL}/public/tags/popular?language=zh`);
        if (!response.data.success || !Array.isArray(response.data.data)) {
            throw new Error('获取热门标签失败');
        }
    });
    updateSection(section, passed);
    
    // 获取统计信息
    passed = await runTest('获取站点统计', async () => {
        const response = await axios.get(`${API_URL}/public/stats?language=zh`);
        if (!response.data.success || !response.data.data) {
            throw new Error('获取站点统计失败');
        }
    });
    updateSection(section, passed);
    
    endSection(section);
}

/**
 * 10. 错误处理测试
 */
async function testErrorHandling() {
    const section = startSection('错误处理');
    
    // 404错误
    let passed = await runTest('404错误处理', async () => {
        try {
            await axios.get(`${API_URL}/non-existent-endpoint`);
            throw new Error('应该返回404错误');
        } catch (error) {
            if (error.response?.status !== 404) {
                throw new Error('应该返回404状态码');
            }
        }
    });
    updateSection(section, passed);
    
    // 无效参数错误
    passed = await runTest('无效参数错误', async () => {
        try {
            await axios.get(`${API_URL}/admin/articles/invalid-id`, {
                headers: { Authorization: `Bearer ${authToken}` }
            });
            throw new Error('应该返回400错误');
        } catch (error) {
            if (error.response?.status !== 400 && error.response?.status !== 404) {
                throw new Error('应该返回400或404状态码');
            }
        }
    });
    updateSection(section, passed);
    
    // 无效的JSON
    passed = await runTest('无效的JSON请求', async () => {
        try {
            await axios.post(`${API_URL}/admin/articles`, 'invalid json', {
                headers: {
                    Authorization: `Bearer ${authToken}`,
                    'Content-Type': 'application/json'
                }
            });
            throw new Error('应该返回400错误');
        } catch (error) {
            if (error.response?.status !== 400) {
                throw new Error('应该返回400状态码');
            }
        }
    });
    updateSection(section, passed);
    
    endSection(section);
}

/**
 * 主测试函数
 */
async function runAllTests() {
    console.log(`${colors.blue}${'='.repeat(60)}${colors.reset}`);
    console.log(`${colors.blue}步骤40: 后端API综合测试${colors.reset}`);
    console.log(`${colors.blue}${'='.repeat(60)}${colors.reset}`);
    console.log(`${colors.yellow}开始时间: ${testResults.startTime.toISOString()}${colors.reset}`);
    console.log(`${colors.yellow}服务器地址: ${BASE_URL}${colors.reset}\n`);
    
    try {
        // 运行所有测试套件
        await testHealthAndBasics();
        await testAuthentication();
        await testCategories();
        await testArticles();
        await testComments();
        await testTranslation();
        await testConfiguration();
        await testMedia();
        await testPublicAPIs();
        await testErrorHandling();
        
    } catch (error) {
        console.error(`${colors.red}测试执行过程中发生严重错误:${colors.reset}`, error);
    }
    
    // 生成测试报告
    testResults.endTime = new Date();
    testResults.duration = testResults.endTime - testResults.startTime;
    
    console.log(`\n${colors.blue}${'='.repeat(60)}${colors.reset}`);
    console.log(`${colors.blue}测试报告总结${colors.reset}`);
    console.log(`${colors.blue}${'='.repeat(60)}${colors.reset}\n`);
    
    // 整体统计
    const passRate = testResults.total > 0 ? 
        ((testResults.passed / testResults.total) * 100).toFixed(2) : 0;
    
    console.log(`${colors.cyan}总测试数: ${testResults.total}${colors.reset}`);
    console.log(`${colors.green}通过: ${testResults.passed}${colors.reset}`);
    console.log(`${colors.red}失败: ${testResults.failed}${colors.reset}`);
    console.log(`${colors.yellow}通过率: ${passRate}%${colors.reset}`);
    console.log(`${colors.yellow}耗时: ${(testResults.duration / 1000).toFixed(2)}秒${colors.reset}\n`);
    
    // 各章节统计
    console.log(`${colors.cyan}章节详情:${colors.reset}`);
    for (const [name, section] of Object.entries(testResults.sections)) {
        const sectionPassRate = section.total > 0 ? 
            ((section.passed / section.total) * 100).toFixed(0) : 0;
        const status = sectionPassRate === '100' ? colors.green : 
                       sectionPassRate >= '80' ? colors.yellow : colors.red;
        console.log(`  ${name}: ${status}${sectionPassRate}%${colors.reset} (${section.passed}/${section.total})`);
    }
    
    // 失败详情
    if (testResults.errors.length > 0) {
        console.log(`\n${colors.red}失败测试详情:${colors.reset}`);
        testResults.errors.forEach((error, index) => {
            console.log(`\n${index + 1}. ${error.test}`);
            console.log(`   状态码: ${error.status || 'N/A'}`);
            console.log(`   错误信息: ${error.error}`);
            if (error.details) {
                console.log(`   详细信息: ${JSON.stringify(error.details, null, 2)}`);
            }
        });
    }
    
    // 保存测试结果到文件
    const resultFile = path.join(__dirname, 'test-results-step40.json');
    fs.writeFileSync(resultFile, JSON.stringify(testResults, null, 2));
    console.log(`\n${colors.cyan}测试结果已保存到: ${resultFile}${colors.reset}`);
    
    // 最终结论
    console.log(`\n${colors.blue}${'='.repeat(60)}${colors.reset}`);
    if (passRate >= 90) {
        console.log(`${colors.green}✅ 测试通过！API系统运行良好。${colors.reset}`);
    } else if (passRate >= 70) {
        console.log(`${colors.yellow}⚠️ 测试部分通过，需要修复失败的测试。${colors.reset}`);
    } else {
        console.log(`${colors.red}❌ 测试失败较多，需要立即修复。${colors.reset}`);
    }
    console.log(`${colors.blue}${'='.repeat(60)}${colors.reset}`);
    
    // 返回是否所有测试都通过
    return testResults.failed === 0;
}

// 检查服务器是否运行
async function checkServerRunning() {
    try {
        await axios.get(`${BASE_URL}/health`);
        return true;
    } catch (error) {
        return false;
    }
}

// 主执行入口
async function main() {
    try {
        // 检查服务器
        const serverRunning = await checkServerRunning();
        if (!serverRunning) {
            console.error(`${colors.red}错误: 服务器未运行在 ${BASE_URL}${colors.reset}`);
            console.log(`${colors.yellow}请先启动服务器: cd backend && npm start${colors.reset}`);
            process.exit(1);
        }
        
        // 运行测试
        const allPassed = await runAllTests();
        
        // 设置退出码
        process.exit(allPassed ? 0 : 1);
        
    } catch (error) {
        console.error(`${colors.red}执行测试时发生错误:${colors.reset}`, error);
        process.exit(1);
    }
}

// 运行测试
main();