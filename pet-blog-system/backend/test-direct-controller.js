/**
 * 直接测试控制器方法是否是Express兼容的函数
 */

const TranslationController = require('./src/controllers/translationController');

console.log('translateArticle类型:', typeof TranslationController.translateArticle);
console.log('translateArticle是函数吗?', typeof TranslationController.translateArticle === 'function');
console.log('translateArticle长度:', TranslationController.translateArticle.length);

// 创建模拟的req和res对象
const mockReq = { params: {}, body: {}, query: {} };
const mockRes = { 
    json: function(data) { console.log('Response:', data); return this; },
    status: function(code) { console.log('Status:', code); return this; }
};
const mockNext = function(err) { if (err) console.log('Next error:', err); };

// 尝试调用这个函数
try {
    const result = TranslationController.translateArticle(mockReq, mockRes, mockNext);
    console.log('调用结果:', result);
    console.log('调用结果类型:', typeof result);
} catch (error) {
    console.error('调用错误:', error.message);
}

// 检查ArticleController作为对比
const ArticleController = require('./src/controllers/articleController');
console.log('\nArticleController.createArticle类型:', typeof ArticleController.createArticle);
console.log('ArticleController.createArticle长度:', ArticleController.createArticle.length);

// 查看authMiddleware是什么
const authMiddleware = require('./src/middleware/auth');
console.log('\nauthMiddleware类型:', typeof authMiddleware);
console.log('authMiddleware是函数吗?', typeof authMiddleware === 'function');