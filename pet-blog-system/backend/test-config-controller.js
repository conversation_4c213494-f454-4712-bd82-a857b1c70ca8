/**
 * 配置控制器测试脚本
 * 
 * 测试配置控制器的所有功能：
 * - 站点配置管理
 * - 域名配置管理
 * - API配置管理
 * 
 * 运行: node test-config-controller.js
 */

const ConfigModel = require('./src/models/config');

// 模拟请求和响应对象
function mockRequest(params = {}, query = {}, body = {}, user = null) {
    return {
        params,
        query,
        body,
        user
    };
}

function mockResponse() {
    const res = {};
    res.status = function(code) {
        res.statusCode = code;
        return res;
    };
    res.json = function(data) {
        res.jsonData = data;
        console.log('响应:', JSON.stringify(data, null, 2));
        return res;
    };
    return res;
}

async function testConfigModel() {
    console.log('\n🧪 测试配置模型...\n');
    
    try {
        // 测试站点配置
        console.log('1. 测试站点配置操作:');
        
        // 更新站点配置
        const siteConfigData = {
            site_name: '测试站点名称',
            site_description: '这是一个测试站点描述',
            google_analytics_code: 'GA-TEST-123',
            google_adsense_code: 'ca-pub-test',
            ads_enabled: true
        };
        
        const updatedSiteConfig = await ConfigModel.updateSiteConfig('en', siteConfigData);
        console.log('✅ 站点配置更新成功:', updatedSiteConfig.site_name);
        
        // 获取站点配置
        const retrievedConfig = await ConfigModel.getSiteConfig('en');
        console.log('✅ 获取站点配置成功:', retrievedConfig ? retrievedConfig.site_name : '无配置');
        
        // 获取所有站点配置
        const allConfigs = await ConfigModel.getAllSiteConfigs();
        console.log('✅ 获取所有站点配置成功，共', allConfigs.length, '个');
        
        // 测试API配置
        console.log('\\n2. 测试API配置操作:');
        
        // 更新API配置
        const apiConfigData = {
            name: '测试API配置',
            api_url: 'https://test-api.example.com',
            api_key: 'test-key-123456',
            model_name: 'test-model-v1',
            is_active: true
        };
        
        const updatedApiConfig = await ConfigModel.updateApiConfig(apiConfigData);
        console.log('✅ API配置更新成功:', updatedApiConfig.name);
        
        // 获取所有API配置
        const allApiConfigs = await ConfigModel.getAllApiConfigs();
        console.log('✅ 获取所有API配置成功，共', allApiConfigs.length, '个');
        
        // 获取激活的API配置
        const activeApiConfig = await ConfigModel.getActiveApiConfig();
        console.log('✅ 获取激活API配置成功:', activeApiConfig ? activeApiConfig.name : '无配置');
        
        // 测试域名配置
        console.log('\\n3. 测试域名配置操作:');
        
        // 添加域名配置
        const domainData = {
            domain: 'test-example.com',
            language: 'en',
            is_active: true
        };
        
        try {
            const newDomain = await ConfigModel.addDomain(domainData);
            console.log('✅ 域名配置创建成功:', newDomain.domain);
            
            // 更新域名配置
            const updatedDomain = await ConfigModel.updateDomain(newDomain.id, {
                domain: 'updated-test-example.com'
            });
            console.log('✅ 域名配置更新成功:', updatedDomain.domain);
            
            // 获取所有域名配置
            const allDomains = await ConfigModel.getAllDomains();
            console.log('✅ 获取所有域名配置成功，共', allDomains.length, '个');
            
            // 删除测试域名配置
            const deleteSuccess = await ConfigModel.deleteDomain(newDomain.id);
            console.log('✅ 域名配置删除成功:', deleteSuccess);
            
        } catch (domainError) {
            if (domainError.message.includes('域名已存在')) {
                console.log('⚠️  域名已存在，跳过创建测试');
                
                // 获取现有域名进行更新测试
                const existingDomain = await ConfigModel.getDomainByName(domainData.domain);
                if (existingDomain) {
                    console.log('✅ 获取现有域名配置成功:', existingDomain.domain);
                }
            } else {
                throw domainError;
            }
        }
        
        console.log('\\n✅ 配置模型测试完成！');
        return true;
        
    } catch (error) {
        console.error('❌ 配置模型测试失败:', error.message);
        return false;
    }
}

async function testConfigController() {
    console.log('\\n🧪 测试配置控制器...');
    
    const ConfigController = require('./src/controllers/configController');
    const mockUser = { id: 1, username: 'testadmin' };
    
    try {
        // 测试获取公开站点配置
        console.log('\\n1. 测试获取公开站点配置:');
        
        const req1 = mockRequest({}, { language: 'en' });
        const res1 = mockResponse();
        
        await ConfigController.getPublicSiteConfig(req1, res1);
        console.log('✅ 获取公开站点配置测试完成');
        
        // 测试参数验证
        const req1Invalid = mockRequest({}, {});
        const res1Invalid = mockResponse();
        
        await ConfigController.getPublicSiteConfig(req1Invalid, res1Invalid);
        console.log('✅ 参数验证测试完成');
        
        // 测试更新站点配置
        console.log('\\n2. 测试更新站点配置:');
        
        const req2 = mockRequest(
            { language: 'en' },
            {},
            {
                site_name: '控制器测试站点',
                site_description: '通过控制器更新的站点描述',
                ads_enabled: false
            },
            mockUser
        );
        const res2 = mockResponse();
        
        await ConfigController.updateSiteConfig(req2, res2);
        console.log('✅ 更新站点配置测试完成');
        
        // 测试获取所有站点配置
        console.log('\\n3. 测试获取所有站点配置:');
        
        const req3 = mockRequest({}, {}, {}, mockUser);
        const res3 = mockResponse();
        
        await ConfigController.getAllSiteConfigs(req3, res3);
        console.log('✅ 获取所有站点配置测试完成');
        
        // 测试域名管理
        console.log('\\n4. 测试域名管理:');
        
        // 添加域名
        const req4 = mockRequest(
            {},
            {},
            {
                domain: 'controller-test.com',
                language: 'en',
                is_active: true
            },
            mockUser
        );
        const res4 = mockResponse();
        
        try {
            await ConfigController.addDomain(req4, res4);
            console.log('✅ 添加域名配置测试完成');
            
            // 获取域名列表
            const req5 = mockRequest({}, {}, {}, mockUser);
            const res5 = mockResponse();
            
            await ConfigController.getAllDomains(req5, res5);
            console.log('✅ 获取域名列表测试完成');
            
        } catch (domainError) {
            console.log('⚠️  域名操作测试跳过（可能已存在）');
        }
        
        // 测试API配置管理
        console.log('\\n5. 测试API配置管理:');
        
        const req6 = mockRequest(
            {},
            {},
            {
                api_url: 'https://controller-test-api.example.com',
                api_key: 'controller-test-key-123',
                model_name: 'controller-test-model'
            },
            mockUser
        );
        const res6 = mockResponse();
        
        await ConfigController.updateApiConfig(req6, res6);
        console.log('✅ 更新API配置测试完成');
        
        // 获取API配置列表
        const req7 = mockRequest({}, {}, {}, mockUser);
        const res7 = mockResponse();
        
        await ConfigController.getAllApiConfigs(req7, res7);
        console.log('✅ 获取API配置列表测试完成');
        
        // 获取激活的API配置
        const req8 = mockRequest({}, {}, {}, mockUser);
        const res8 = mockResponse();
        
        await ConfigController.getActiveApiConfig(req8, res8);
        console.log('✅ 获取激活API配置测试完成');
        
        console.log('\\n✅ 配置控制器测试完成！');
        return true;
        
    } catch (error) {
        console.error('❌ 配置控制器测试失败:', error.message);
        console.error('错误详情:', error);
        return false;
    }
}

async function runTests() {
    console.log('🚀 开始配置控制器完整测试\\n');
    
    try {
        // 测试数据库连接
        console.log('📡 测试数据库连接...');
        const { healthCheck } = require('./src/config/database');
        const healthResult = await healthCheck();
        
        if (healthResult.status !== 'healthy') {
            console.error('❌ 数据库连接失败，终止测试');
            console.error('健康检查结果:', healthResult);
            return;
        }
        
        console.log('✅ 数据库连接成功');
        
        // 测试模型
        const modelTestResult = await testConfigModel();
        if (!modelTestResult) {
            console.error('❌ 模型测试失败，终止测试');
            return;
        }
        
        // 测试控制器
        const controllerTestResult = await testConfigController();
        if (!controllerTestResult) {
            console.error('❌ 控制器测试失败');
            return;
        }
        
        console.log('\\n🎉 所有测试通过！配置控制器功能正常');
        console.log('\\n📋 测试总结:');
        console.log('- ✅ 数据库连接正常');
        console.log('- ✅ 配置模型功能正常');
        console.log('- ✅ 配置控制器功能正常');
        console.log('- ✅ 站点配置管理正常');
        console.log('- ✅ 域名配置管理正常'); 
        console.log('- ✅ API配置管理正常');
        
    } catch (error) {
        console.error('❌ 测试执行失败:', error.message);
        console.error('错误详情:', error);
    }
}

// 直接运行测试
runTests();