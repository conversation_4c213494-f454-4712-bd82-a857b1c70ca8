/**
 * 评论路由
 * 
 * 处理评论系统的所有路由
 * 包括管理后台和公开API
 * 
 * 管理后台路由（需要认证）：
 * - GET    /admin/comments                    获取评论列表（支持筛选）
 * - GET    /admin/comments/pending            获取待审核评论列表
 * - GET    /admin/comments/stats              获取评论统计信息
 * - PUT    /admin/comments/:id/status         审核单个评论
 * - POST   /admin/comments/batch-approve      批量审核评论
 * - DELETE /admin/comments/:id               删除评论
 * 
 * 公开路由（无需认证）：
 * - GET    /public/comments                   获取文章评论列表
 * - POST   /public/comments                   提交新评论
 * 
 * <AUTHOR> Blog System
 * @version 1.0.0
 * @date 2025-01-09
 */

const express = require('express');
const { body, query, param } = require('express-validator');
const CommentController = require('../controllers/commentController');
const { requireAuth } = require('../middleware/auth');

const router = express.Router();

// ====================================
// 参数验证规则
// ====================================

/**
 * 提交评论参数验证
 */
const submitCommentValidation = [
    body('article_id')
        .notEmpty()
        .withMessage('文章ID不能为空')
        .isInt({ min: 1 })
        .withMessage('文章ID必须是正整数'),
    
    body('language')
        .notEmpty()
        .withMessage('语言代码不能为空')
        .isIn(['en', 'de', 'ru', 'zh'])
        .withMessage('语言代码无效'),
    
    body('parent_id')
        .optional({ nullable: true })
        .isInt({ min: 1 })
        .withMessage('父评论ID必须是正整数'),
    
    body('username')
        .notEmpty()
        .withMessage('用户名不能为空')
        .isLength({ min: 2, max: 50 })
        .withMessage('用户名长度必须在2-50字符之间')
        .matches(/^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/)
        .withMessage('用户名只能包含字母、数字、中文、下划线和连字符'),
    
    body('email')
        .notEmpty()
        .withMessage('邮箱不能为空')
        .isEmail()
        .withMessage('邮箱格式无效')
        .normalizeEmail(),
    
    body('content')
        .notEmpty()
        .withMessage('评论内容不能为空')
        .isLength({ min: 5, max: 2000 })
        .withMessage('评论内容长度必须在5-2000字符之间')
        .custom((value) => {
            // 检查是否包含有效文字（非纯符号）
            if (!/[a-zA-Z0-9\u4e00-\u9fa5]/.test(value.trim())) {
                throw new Error('评论内容必须包含有效文字');
            }
            return true;
        })
];

/**
 * 获取文章评论参数验证
 */
const getArticleCommentsValidation = [
    query('article_id')
        .notEmpty()
        .withMessage('文章ID不能为空')
        .isInt({ min: 1 })
        .withMessage('文章ID必须是正整数'),
    
    query('language')
        .notEmpty()
        .withMessage('语言代码不能为空')
        .isIn(['en', 'de', 'ru', 'zh'])
        .withMessage('语言代码无效'),
    
    query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('页码必须是正整数'),
    
    query('limit')
        .optional()
        .isInt({ min: 1, max: 50 })
        .withMessage('每页数量必须在1-50之间')
];

/**
 * 管理员获取评论参数验证
 */
const getAdminCommentsValidation = [
    query('status')
        .optional()
        .isIn(['pending', 'approved', 'rejected'])
        .withMessage('状态参数无效'),
    
    query('language')
        .optional()
        .isIn(['en', 'de', 'ru', 'zh'])
        .withMessage('语言代码无效'),
    
    query('keyword')
        .optional()
        .isLength({ max: 100 })
        .withMessage('搜索关键词长度不能超过100字符'),
    
    query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('页码必须是正整数'),
    
    query('limit')
        .optional()
        .isInt({ min: 1, max: 200 })
        .withMessage('每页数量必须在1-200之间')
];

/**
 * 更新评论状态参数验证
 */
const updateCommentStatusValidation = [
    param('id')
        .isInt({ min: 1 })
        .withMessage('评论ID必须是正整数'),
    
    body('status')
        .notEmpty()
        .withMessage('状态不能为空')
        .isIn(['pending', 'approved', 'rejected'])
        .withMessage('状态只能是: pending, approved, rejected')
];

/**
 * 批量操作评论参数验证
 */
const batchUpdateCommentsValidation = [
    body('ids')
        .isArray({ min: 1, max: 100 })
        .withMessage('评论ID列表必须是数组，且包含1-100个元素'),
    
    body('ids.*')
        .isInt({ min: 1 })
        .withMessage('每个评论ID必须是正整数'),
    
    body('status')
        .optional()
        .isIn(['pending', 'approved', 'rejected'])
        .withMessage('状态只能是: pending, approved, rejected')
];

/**
 * 删除评论参数验证
 */
const deleteCommentValidation = [
    param('id')
        .isInt({ min: 1 })
        .withMessage('评论ID必须是正整数'),
    
    query('force')
        .optional()
        .isBoolean()
        .withMessage('force参数必须是布尔值')
];

/**
 * 获取统计信息参数验证
 */
const getStatsValidation = [
    query('article_id')
        .optional()
        .isInt({ min: 1 })
        .withMessage('文章ID必须是正整数')
];

// ====================================
// 公开API路由（无需认证）
// ====================================

/**
 * 获取文章评论列表
 * GET /public/comments?article_id=1&language=en&page=1&limit=20
 */
router.get('/public/comments', 
    getArticleCommentsValidation,
    CommentController.getArticleComments
);

/**
 * 提交新评论
 * POST /public/comments
 */
router.post('/public/comments', 
    submitCommentValidation,
    CommentController.submitComment
);

// ====================================
// 管理API路由（需要JWT认证）
// ====================================

/**
 * 获取评论列表（支持筛选）
 * GET /admin/comments?status=pending&language=en&page=1&limit=50
 */
router.get('/admin/comments', 
    requireAuth,
    getAdminCommentsValidation,
    CommentController.getAdminComments
);

/**
 * 获取待审核评论列表
 * GET /admin/comments/pending?page=1&limit=50
 */
router.get('/admin/comments/pending', 
    requireAuth,
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('limit').optional().isInt({ min: 1, max: 200 }).withMessage('每页数量必须在1-200之间'),
    CommentController.getPendingComments
);

/**
 * 获取待审核评论数量
 * GET /admin/comments/pending-count
 */
router.get('/admin/comments/pending-count',
    requireAuth,
    CommentController.getPendingCount
);

/**
 * 获取评论统计信息
 * GET /admin/comments/stats?article_id=1
 */
router.get('/admin/comments/stats',
    requireAuth,
    getStatsValidation,
    CommentController.getCommentStats
);

/**
 * 审核单个评论（更新状态）
 * PUT /admin/comments/:id/status
 */
router.put('/admin/comments/:id/status', 
    requireAuth,
    updateCommentStatusValidation,
    CommentController.updateCommentStatus
);

/**
 * 批量审核评论
 * POST /admin/comments/batch-approve
 */
router.post('/admin/comments/batch-approve', 
    requireAuth,
    batchUpdateCommentsValidation,
    CommentController.batchUpdateComments
);

/**
 * 删除评论
 * DELETE /admin/comments/:id?force=false
 */
router.delete('/admin/comments/:id', 
    requireAuth,
    deleteCommentValidation,
    CommentController.deleteComment
);

// ====================================
// 中间件：验证结果处理
// ====================================

/**
 * 验证错误处理中间件
 * 统一处理所有路由的验证错误
 */
const { validationResult } = require('express-validator');

// 在所有路由之前添加验证结果检查
router.use((req, res, next) => {
    const errors = validationResult(req);
    
    if (!errors.isEmpty()) {
        const formattedErrors = errors.array().map(error => ({
            field: error.path || error.param,
            message: error.msg,
            value: error.value,
            location: error.location
        }));
        
        return res.status(400).json({
            success: false,
            code: 400,
            message: '请求参数验证失败',
            data: {
                errors: formattedErrors,
                count: formattedErrors.length
            },
            timestamp: Date.now()
        });
    }
    
    next();
});

// ====================================
// 错误处理
// ====================================

/**
 * 评论路由专用错误处理中间件
 */
router.use((error, req, res, next) => {
    console.error('评论路由错误:', error);
    
    // 数据库连接错误
    if (error.code === 'ECONNREFUSED' || error.code === 'ER_BAD_DB_ERROR') {
        return res.status(500).json({
            success: false,
            code: 500,
            message: '数据库连接失败，请稍后重试',
            timestamp: Date.now()
        });
    }
    
    // 其他未处理错误
    return res.status(500).json({
        success: false,
        code: 500,
        message: '服务器内部错误',
        timestamp: Date.now()
    });
});

module.exports = router;