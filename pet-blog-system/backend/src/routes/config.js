/**
 * 配置管理路由
 * 处理站点配置相关的API请求
 */

const express = require('express');
const router = express.Router();
const ConfigController = require('../controllers/configController');
const { requireAuth } = require('../middleware/auth');
// const { validateLanguage } = require('../middleware/validation'); // TODO: 需要实现validateLanguage

// ====================================
// 公开API路由
// ====================================

/**
 * 获取站点配置
 * GET /api/public/site-config
 */
router.get('/public/site-config', 
    // validateLanguage, // TODO: 需要实现validateLanguage中间件
    ConfigController.getPublicSiteConfig
);

// ====================================
// 管理API路由（需要认证）
// ====================================

/**
 * 获取所有站点配置
 * GET /api/admin/configs
 */
router.get('/admin/configs',
    requireAuth,
    ConfigController.getAllSiteConfigs
);

/**
 * 更新站点配置
 * PUT /api/admin/configs/:language
 */
router.put('/admin/configs/:language',
    requireAuth,
    ConfigController.updateSiteConfig
);

/**
 * 获取所有域名配置
 * GET /api/admin/domains
 */
router.get('/admin/domains',
    requireAuth,
    ConfigController.getAllDomains
);

/**
 * 添加域名
 * POST /api/admin/domains
 */
router.post('/admin/domains',
    requireAuth,
    ConfigController.addDomain
);

/**
 * 更新域名
 * PUT /api/admin/domains/:id
 */
router.put('/admin/domains/:id',
    requireAuth,
    ConfigController.updateDomain
);

/**
 * 删除域名
 * DELETE /api/admin/domains/:id
 */
router.delete('/admin/domains/:id',
    requireAuth,
    ConfigController.deleteDomain
);

/**
 * 获取所有API配置
 * GET /api/admin/api-configs
 */
router.get('/admin/api-configs',
    requireAuth,
    ConfigController.getAllApiConfigs
);

/**
 * 获取当前激活的API配置
 * GET /api/admin/api-configs/active
 */
router.get('/admin/api-configs/active',
    requireAuth,
    ConfigController.getActiveApiConfig
);

/**
 * 更新API配置
 * PUT /api/admin/api-configs/:id
 */
router.put('/admin/api-configs/:id',
    requireAuth,
    ConfigController.updateApiConfig
);

module.exports = router;