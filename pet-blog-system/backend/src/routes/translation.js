/**
 * 翻译管理路由
 * 处理文章和内容翻译相关的API请求
 */

const express = require('express');
const router = express.Router();
const TranslationController = require('../controllers/translationController');
const { requireAuth } = require('../middleware/auth');

// ====================================
// 管理API路由（需要认证）
// ====================================

/**
 * 翻译文章
 * POST /api/admin/articles/:id/translate
 */
router.post('/admin/articles/:id/translate',
    requireAuth,
    TranslationController.translateArticle
);

/**
 * 更新翻译内容
 * PUT /api/admin/articles/:id/translations/:language
 */
router.put('/admin/articles/:id/translations/:language',
    requireAuth,
    TranslationController.updateTranslation
);

/**
 * 发布翻译
 * POST /api/admin/articles/:id/translations/:language/publish
 */
router.post('/admin/articles/:id/translations/:language/publish',
    requireAuth,
    TranslationController.publishTranslation
);

/**
 * 获取翻译历史
 * GET /api/admin/articles/:id/translation-history
 */
router.get('/admin/articles/:id/translation-history',
    requireAuth,
    TranslationController.getTranslationHistory
);

/**
 * 批量翻译文章
 * POST /api/admin/articles/batch-translate
 */
router.post('/admin/articles/batch-translate',
    requireAuth,
    TranslationController.batchTranslate
);

/**
 * 获取翻译配置
 * GET /api/admin/translation-config
 */
router.get('/admin/translation-config',
    requireAuth,
    TranslationController.getTranslationConfig
);

module.exports = router;