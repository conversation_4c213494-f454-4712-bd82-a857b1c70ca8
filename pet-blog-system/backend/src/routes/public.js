/**
 * 公开API路由
 * 
 * 处理前端站点使用的所有公开API路由，无需认证
 * 包括文章、分类、评论、搜索和站点配置功能
 * 
 * 路由列表：
 * - GET    /public/articles                    获取文章列表
 * - GET    /public/articles/:slug              获取文章详情
 * - POST   /public/articles/:id/view           增加文章浏览量
 * - GET    /public/categories                  获取分类列表
 * - GET    /public/comments                    获取文章评论
 * - POST   /public/comments                    提交评论
 * - GET    /public/search                      搜索文章
 * - GET    /public/site-config                 获取站点配置
 * 
 * 安全特性：
 * - 参数验证和清理
 * - 限流保护
 * - XSS防护
 * - 用户封禁检查
 * - IP和用户代理记录
 * 
 * <AUTHOR> Blog System
 * @version 1.0.0
 */

const express = require('express');
const { body, query, param } = require('express-validator');
const PublicController = require('../controllers/publicController');

const router = express.Router();

// ====================================
// 参数验证规则
// ====================================

/**
 * 语言参数验证（通用）
 */
const languageValidation = [
    query('language')
        .notEmpty()
        .withMessage('语言代码参数必需')
        .isIn(['en', 'de', 'ru', 'zh'])
        .withMessage('语言代码必须是: en, de, ru, zh之一')
];

/**
 * 分页参数验证（通用）
 */
const paginationValidation = [
    query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('页码必须是正整数'),
    
    query('limit')
        .optional()
        .isInt({ min: 1, max: 50 })
        .withMessage('每页数量必须在1-50之间')
];

/**
 * 文章列表参数验证
 * GET /public/articles?language=en&category_id=1&page=1&limit=10&search=keyword
 */
const articleListValidation = [
    ...languageValidation,
    
    query('category_id')
        .optional()
        .isInt({ min: 1 })
        .withMessage('分类ID必须是正整数'),
    
    ...paginationValidation,
    
    query('search')
        .optional()
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('搜索关键词长度必须在1-100个字符之间')
        .matches(/^[a-zA-Z0-9\s\u4e00-\u9fa5\-_.,!?]*$/)
        .withMessage('搜索关键词包含无效字符')
];

/**
 * 文章详情参数验证
 * GET /public/articles/:slug?language=en
 */
const articleDetailValidation = [
    param('slug')
        .trim()
        .notEmpty()
        .withMessage('文章别名不能为空')
        .isLength({ min: 1, max: 255 })
        .withMessage('文章别名长度必须在1-255个字符之间')
        .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
        .withMessage('文章别名格式无效（只能包含小写字母、数字和连字符）'),
    
    ...languageValidation
];

/**
 * 增加浏览量参数验证
 * POST /public/articles/:id/view
 */
const viewCountValidation = [
    param('id')
        .isInt({ min: 1 })
        .withMessage('文章ID必须是正整数'),
    
    body('language')
        .notEmpty()
        .withMessage('语言代码参数必需')
        .isIn(['en', 'de', 'ru'])
        .withMessage('语言代码必须是: en, de, ru之一')
];

/**
 * 分类列表参数验证
 * GET /public/categories?language=en&type=cat
 */
const categoryListValidation = [
    ...languageValidation,
    
    query('type')
        .optional()
        .isIn(['cat', 'dog'])
        .withMessage('分类类型必须是cat或dog')
];

/**
 * 评论列表参数验证
 * GET /public/comments?article_id=1&language=en&page=1&limit=20
 */
const commentListValidation = [
    query('article_id')
        .notEmpty()
        .withMessage('文章ID参数必需')
        .isInt({ min: 1 })
        .withMessage('文章ID必须是正整数'),
    
    ...languageValidation,
    
    query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('页码必须是正整数'),
    
    query('limit')
        .optional()
        .isInt({ min: 1, max: 50 })
        .withMessage('每页数量必须在1-50之间')
];

/**
 * 提交评论参数验证
 * POST /public/comments
 */
const submitCommentValidation = [
    body('article_id')
        .notEmpty()
        .withMessage('文章ID参数必需')
        .isInt({ min: 1 })
        .withMessage('文章ID必须是正整数'),
    
    body('language')
        .notEmpty()
        .withMessage('语言代码参数必需')
        .isIn(['en', 'de', 'ru'])
        .withMessage('语言代码必须是: en, de, ru之一'),
    
    body('parent_id')
        .optional()
        .isInt({ min: 1 })
        .withMessage('父评论ID必须是正整数'),
    
    body('username')
        .trim()
        .notEmpty()
        .withMessage('用户名不能为空')
        .isLength({ min: 2, max: 50 })
        .withMessage('用户名长度必须在2-50个字符之间')
        .matches(/^[a-zA-Z0-9\u4e00-\u9fa5_\-\s]+$/)
        .withMessage('用户名只能包含字母、数字、中文、下划线、连字符和空格'),
    
    body('email')
        .trim()
        .notEmpty()
        .withMessage('邮箱不能为空')
        .isEmail()
        .withMessage('邮箱格式不正确')
        .isLength({ max: 100 })
        .withMessage('邮箱长度不能超过100个字符')
        .normalizeEmail(),
    
    body('content')
        .trim()
        .notEmpty()
        .withMessage('评论内容不能为空')
        .isLength({ min: 5, max: 1000 })
        .withMessage('评论内容长度必须在5-1000个字符之间')
        .matches(/^[^<>]*$/)
        .withMessage('评论内容不能包含HTML标签')
];

/**
 * 搜索参数验证
 * GET /public/search?q=keyword&language=en&type=article&page=1&limit=10
 */
const searchValidation = [
    query('q')
        .trim()
        .notEmpty()
        .withMessage('搜索关键词不能为空')
        .isLength({ min: 1, max: 100 })
        .withMessage('搜索关键词长度必须在1-100个字符之间')
        .matches(/^[a-zA-Z0-9\s\u4e00-\u9fa5\-_.,!?]*$/)
        .withMessage('搜索关键词包含无效字符'),
    
    ...languageValidation,
    
    query('type')
        .optional()
        .isIn(['article', 'category'])
        .withMessage('搜索类型必须是article或category'),
    
    ...paginationValidation
];

/**
 * 站点配置参数验证
 * GET /public/site-config?language=en
 */
const siteConfigValidation = [
    ...languageValidation
];

// ====================================
// 公开API路由定义
// ====================================

/**
 * 获取文章列表
 * GET /public/articles?language=en&category_id=1&page=1&limit=10&search=keyword
 * 
 * 功能说明：
 * - 分页获取已发布的文章列表
 * - 支持按分类筛选
 * - 支持关键词搜索
 * - 返回文章基本信息和分页信息
 */
router.get('/articles', articleListValidation, PublicController.getArticles);

/**
 * 获取文章详情
 * GET /public/articles/:slug?language=en
 * 
 * 功能说明：
 * - 根据URL别名获取文章详情
 * - 包含完整内容和SEO元数据
 * - 自动获取相关文章推荐
 */
router.get('/articles/:slug', articleDetailValidation, PublicController.getArticleBySlug);

/**
 * 增加文章浏览量
 * POST /public/articles/:id/view
 * Body: { "language": "en" }
 *
 * 功能说明：
 * - 异步增加文章浏览量统计
 * - 用于热门文章排序和统计分析
 */
router.post('/articles/:id/view', viewCountValidation, PublicController.incrementArticleView);

/**
 * 获取文章评论（RESTful风格）
 * GET /public/articles/:id/comments?language=en&page=1&limit=20
 *
 * 功能说明：
 * - 获取指定文章的评论列表
 * - RESTful风格的URL设计
 * - 与 /public/comments 功能相同，但URL更语义化
 */
router.get('/articles/:id/comments', [
    param('id')
        .isInt({ min: 1 })
        .withMessage('文章ID必须是正整数'),
    ...languageValidation,
    query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('页码必须是正整数'),
    query('limit')
        .optional()
        .isInt({ min: 1, max: 50 })
        .withMessage('每页数量必须在1-50之间')
], PublicController.getArticleComments);

/**
 * 获取分类列表
 * GET /public/categories?language=en&type=cat
 * 
 * 功能说明：
 * - 获取分类层级结构
 * - 支持按宠物类型筛选（cat/dog）
 * - 返回嵌套的分类树结构
 */
router.get('/categories', categoryListValidation, PublicController.getCategories);

/**
 * 获取文章评论
 * GET /public/comments?article_id=1&language=en&page=1&limit=20
 * 
 * 功能说明：
 * - 分页获取指定文章的评论
 * - 包含评论回复的嵌套结构
 * - 只返回已审核通过的评论
 */
router.get('/comments', commentListValidation, PublicController.getComments);

/**
 * 提交评论
 * POST /public/comments
 * Body: {
 *   "article_id": 1,
 *   "language": "en",
 *   "parent_id": null,
 *   "username": "用户名",
 *   "email": "<EMAIL>",
 *   "content": "评论内容"
 * }
 * 
 * 功能说明：
 * - 提交新评论或回复
 * - 包含完整的验证和安全防护
 * - 记录用户IP和浏览器信息
 * - 新评论需要审核后才能显示
 */
router.post('/comments', submitCommentValidation, PublicController.submitComment);

/**
 * 搜索文章
 * GET /public/search?q=keyword&language=en&type=article&page=1&limit=10
 * 
 * 功能说明：
 * - 全文搜索文章或分类
 * - 支持分页结果
 * - 根据相关性排序
 */
router.get('/search', searchValidation, PublicController.searchArticles);

/**
 * 获取站点配置
 * GET /public/site-config?language=en
 *
 * 功能说明：
 * - 获取前端所需的站点配置
 * - 包含站点名称、描述、广告代码等
 * - 只返回公开配置，不包含敏感信息
 */
router.get('/site-config', siteConfigValidation, PublicController.getSiteConfig);

/**
 * 获取相关文章
 * GET /public/articles/:id/related?language=en&limit=5
 *
 * 功能说明：
 * - 获取与指定文章相关的文章列表
 * - 基于分类、标签等相关性算法
 * - 用于文章详情页的推荐功能
 */
router.get('/articles/:id/related', [
    param('id')
        .isInt({ min: 1 })
        .withMessage('文章ID必须是正整数'),
    ...languageValidation,
    query('limit')
        .optional()
        .isInt({ min: 1, max: 20 })
        .withMessage('限制数量必须在1-20之间')
], PublicController.getRelatedArticles);

/**
 * 获取热门标签
 * GET /public/tags/popular?language=en&limit=10
 *
 * 功能说明：
 * - 获取热门标签列表
 * - 按使用频率排序
 * - 用于标签云和热门标签展示
 */
router.get('/tags/popular', [
    ...languageValidation,
    query('limit')
        .optional()
        .isInt({ min: 1, max: 50 })
        .withMessage('限制数量必须在1-50之间')
], PublicController.getPopularTags);

/**
 * 获取站点统计
 * GET /public/stats?language=en
 *
 * 功能说明：
 * - 获取站点基础统计信息
 * - 包含文章数、评论数等公开数据
 * - 用于站点首页或关于页面展示
 */
router.get('/stats', languageValidation, PublicController.getSiteStats);

// ====================================
// 开发和调试路由 (仅开发环境)
// ====================================

if (process.env.NODE_ENV === 'development') {
    
    /**
     * 获取公开API路由状态 (开发环境专用)
     * GET /public/status
     */
    router.get('/status', (req, res) => {
        const { ResponseUtil } = require('../utils/response');
        
        return ResponseUtil.success(res, {
            routes_loaded: true,
            total_routes: 8,
            routes: [
                { method: 'GET', path: '/articles', description: '获取文章列表' },
                { method: 'GET', path: '/articles/:slug', description: '获取文章详情' },
                { method: 'POST', path: '/articles/:id/view', description: '增加浏览量' },
                { method: 'GET', path: '/categories', description: '获取分类列表' },
                { method: 'GET', path: '/comments', description: '获取评论列表' },
                { method: 'POST', path: '/comments', description: '提交评论' },
                { method: 'GET', path: '/search', description: '搜索文章' },
                { method: 'GET', path: '/site-config', description: '获取站点配置' }
            ],
            security: {
                parameter_validation: true,
                xss_protection: true,
                rate_limiting: true,
                user_ban_check: true,
                ip_logging: true
            },
            environment: process.env.NODE_ENV,
            timestamp: new Date().toISOString()
        }, '公开API路由状态获取成功');
    });
    
    /**
     * 验证所有验证规则 (开发环境专用)
     * POST /public/validate-test
     */
    router.post('/validate-test', (req, res) => {
        const { ResponseUtil } = require('../utils/response');
        const { validationResult } = require('express-validator');
        
        return ResponseUtil.success(res, {
            validation_available: true,
            test_endpoint: '/public/validate-test',
            message: '该端点用于测试参数验证规则',
            usage: '发送任意参数来测试验证规则',
            body: req.body,
            query: req.query,
            params: req.params,
            timestamp: new Date().toISOString()
        }, '验证测试端点正常');
    });
    
    /**
     * 获取限流信息 (开发环境专用)
     * GET /public/rate-limit-info
     */
    router.get('/rate-limit-info', (req, res) => {
        const { ResponseUtil } = require('../utils/response');
        
        return ResponseUtil.success(res, {
            rate_limits: {
                public_api: {
                    limit: '100 requests per minute',
                    window: '60 seconds',
                    scope: 'per IP address'
                },
                comments: {
                    additional_limit: '10 comments per hour per IP',
                    protection: 'Anti-spam measures active'
                },
                search: {
                    additional_limit: '50 searches per minute per IP',
                    caching: 'Search results cached for 5 minutes'
                }
            },
            current_ip: req.ip,
            user_agent: req.get('User-Agent'),
            timestamp: new Date().toISOString()
        }, '限流信息获取成功');
    });
}

// ====================================
// 导出路由模块
// ====================================

module.exports = router;