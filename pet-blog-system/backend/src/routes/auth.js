/**
 * 认证路由
 * 
 * 处理管理员认证相关的路由
 * - 登录
 * - Token刷新  
 * - 获取用户信息
 * - 登出
 * - 修改密码
 * 
 * <AUTHOR> Blog System
 * @version 1.0.0
 */

const express = require('express');
const { body } = require('express-validator');
const AuthController = require('../controllers/authController');
const { requireAuth, verifyRefreshToken } = require('../middleware/auth');

const router = express.Router();

// ====================================
// 参数验证规则
// ====================================

/**
 * 登录参数验证
 */
const loginValidation = [
    body('username')
        .trim()
        .notEmpty()
        .withMessage('用户名不能为空')
        .isLength({ min: 2, max: 50 })
        .withMessage('用户名长度必须在2-50个字符之间')
        .matches(/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/)
        .withMessage('用户名只能包含字母、数字、下划线和中文'),
    
    body('password')
        .notEmpty()
        .withMessage('密码不能为空')
        .isLength({ min: 6, max: 128 })
        .withMessage('密码长度必须在6-128个字符之间')
];

/**
 * 修改密码参数验证
 */
const changePasswordValidation = [
    body('current_password')
        .notEmpty()
        .withMessage('当前密码不能为空'),
    
    body('new_password')
        .isLength({ min: 6, max: 128 })
        .withMessage('新密码长度必须在6-128个字符之间')
        .matches(/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{6,}$/)
        .withMessage('新密码必须包含至少一个字母和一个数字'),
    
    body('confirm_password')
        .custom((value, { req }) => {
            if (value !== req.body.new_password) {
                throw new Error('确认密码与新密码不匹配');
            }
            return true;
        })
];

// ====================================
// 公开路由 (无需认证)
// ====================================

/**
 * 管理员登录
 * POST /admin/auth/login
 * 
 * Body参数:
 * {
 *   "username": "admin",
 *   "password": "password"
 * }
 */
router.post('/login', loginValidation, AuthController.login);

/**
 * 刷新Access Token
 * POST /admin/auth/refresh
 * 
 * Headers:
 *   Authorization: Bearer {refresh_token}
 * 
 * 或 Body参数:
 * {
 *   "refresh_token": "jwt_refresh_token"
 * }
 */
router.post('/refresh', AuthController.refresh);

// ====================================
// 需要认证的路由
// ====================================

/**
 * 获取当前用户信息
 * GET /admin/auth/me
 *
 * Headers:
 *   Authorization: Bearer {access_token}
 */
router.get('/me', requireAuth, AuthController.me);

/**
 * 验证Token有效性
 * GET /admin/auth/verify
 *
 * Headers:
 *   Authorization: Bearer {access_token}
 */
router.get('/verify', requireAuth, AuthController.verify);

/**
 * 获取管理员详细信息（别名为/me）
 * GET /admin/auth/profile
 *
 * Headers:
 *   Authorization: Bearer {access_token}
 */
router.get('/profile', requireAuth, AuthController.me);

/**
 * 管理员登出
 * POST /admin/auth/logout
 * 
 * Headers:
 *   Authorization: Bearer {access_token}
 * 
 * 注意：JWT是无状态的，真正的登出在客户端删除token
 */
router.post('/logout', requireAuth, AuthController.logout);

/**
 * 修改密码
 * POST /admin/auth/change-password
 * 
 * Headers:
 *   Authorization: Bearer {access_token}
 * 
 * Body参数:
 * {
 *   "current_password": "current_password",
 *   "new_password": "new_password", 
 *   "confirm_password": "new_password"
 * }
 */
router.post('/change-password', requireAuth, changePasswordValidation, AuthController.changePassword);

// ====================================
// 开发和调试路由 (仅开发环境)
// ====================================

if (process.env.NODE_ENV === 'development') {
    
    /**
     * 验证Token有效性 (开发环境专用)
     * POST /admin/auth/verify-token
     */
    router.post('/verify-token', (req, res) => {
        const { verifyToken, extractBearerToken } = require('../middleware/auth');
        const { ResponseUtil } = require('../utils/response');
        
        try {
            const token = extractBearerToken(req) || req.body.token;
            
            if (!token) {
                return ResponseUtil.badRequest(res, 'Token缺失');
            }
            
            const decoded = verifyToken(token);
            
            return ResponseUtil.success(res, {
                valid: true,
                decoded: decoded,
                token_type: decoded.type || 'unknown'
            }, 'Token验证成功');
            
        } catch (error) {
            return ResponseUtil.unauthorized(res, `Token验证失败: ${error.message}`);
        }
    });
    
    /**
     * 获取认证状态 (开发环境专用)
     * GET /admin/auth/status
     */
    router.get('/status', (req, res) => {
        const { ResponseUtil } = require('../utils/response');
        
        return ResponseUtil.success(res, {
            authentication_enabled: true,
            jwt_expire: process.env.JWT_EXPIRE || '24h',
            jwt_refresh_expire: process.env.JWT_REFRESH_EXPIRE || '7d',
            environment: process.env.NODE_ENV,
            timestamp: new Date().toISOString()
        }, '认证状态获取成功');
    });
}

module.exports = router;