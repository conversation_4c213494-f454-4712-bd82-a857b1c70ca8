/**
 * 媒体管理路由
 * 
 * 定义媒体上传、管理相关的API路由
 * 包括图片上传、文档上传、文件列表、删除等功能
 * 
 * <AUTHOR> Blog System
 * @version 1.0.0
 */

const router = require('express').Router();
const MediaController = require('../controllers/mediaController');
const { requireAuth } = require('../middleware/auth');
const uploadService = require('../services/uploadService');
const { asyncHandler } = require('../utils/response');

// 配置multer中间件
const upload = uploadService.upload;

/**
 * @route   POST /api/admin/media/upload
 * @desc    上传单个图片
 * @access  Private (Admin)
 */
router.post('/upload', 
    requireAuth,
    upload.single('file'),
    asyncHandler(MediaController.uploadImage)
);

/**
 * @route   POST /api/admin/media/upload-multiple
 * @desc    批量上传图片（最多10个）
 * @access  Private (Admin)
 */
router.post('/upload-multiple',
    requireAuth,
    upload.array('files', 10),
    asyncHandler(MediaController.uploadMultipleImages)
);

/**
 * @route   POST /api/admin/media/upload-document
 * @desc    上传文档文件
 * @access  Private (Admin)
 */
router.post('/upload-document',
    requireAuth,
    upload.single('file'),
    asyncHandler(MediaController.uploadDocument)
);

/**
 * @route   GET /api/admin/media
 * @desc    获取媒体文件列表
 * @access  Private (Admin)
 * @query   type - 文件类型 (all|image|document)
 * @query   page - 页码
 * @query   limit - 每页数量
 */
router.get('/',
    requireAuth,
    asyncHandler(MediaController.getMediaList)
);

/**
 * @route   GET /api/admin/media/upload-config
 * @desc    获取上传配置信息
 * @access  Private (Admin)
 */
router.get('/upload-config',
    requireAuth,
    asyncHandler(MediaController.getUploadConfig)
);

/**
 * @route   GET /api/admin/media/storage-stats
 * @desc    获取存储统计信息
 * @access  Private (Admin)
 */
router.get('/storage-stats',
    requireAuth,
    asyncHandler(MediaController.getStorageStats)
);

/**
 * @route   GET /api/admin/media/stats
 * @desc    获取存储统计信息（别名）
 * @access  Private (Admin)
 */
router.get('/stats',
    requireAuth,
    asyncHandler(MediaController.getStorageStats)
);

/**
 * @route   DELETE /api/admin/media
 * @desc    删除单个媒体文件
 * @access  Private (Admin)
 * @body    path - 文件路径
 */
router.delete('/',
    requireAuth,
    asyncHandler(MediaController.deleteMedia)
);

/**
 * @route   DELETE /api/admin/media/batch
 * @desc    批量删除媒体文件
 * @access  Private (Admin)
 * @body    paths - 文件路径数组
 */
router.delete('/batch',
    requireAuth,
    asyncHandler(MediaController.deleteMultipleMedia)
);

/**
 * @route   POST /api/admin/media/cleanup
 * @desc    清理临时文件
 * @access  Private (Admin)
 */
router.post('/cleanup',
    requireAuth,
    asyncHandler(MediaController.cleanupTemp)
);

// 错误处理中间件（处理multer错误）
router.use((error, req, res, next) => {
    const { ResponseUtil } = require('../utils/response');
    
    if (error.code === 'LIMIT_FILE_SIZE') {
        return ResponseUtil.badRequest(res, '文件大小超过限制（最大5MB）');
    }
    
    if (error.code === 'LIMIT_FILE_COUNT') {
        return ResponseUtil.badRequest(res, '文件数量超过限制');
    }
    
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
        return ResponseUtil.badRequest(res, '不支持的文件字段');
    }
    
    if (error.message && error.message.includes('不支持的文件类型')) {
        return ResponseUtil.badRequest(res, error.message);
    }
    
    console.error('媒体路由错误:', error);
    return ResponseUtil.serverError(res, '文件处理失败，请稍后重试', error);
});

module.exports = router;