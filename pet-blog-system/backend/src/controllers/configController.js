/**
 * 配置控制器
 * 
 * 处理站点配置、域名配置和API配置的管理操作
 * 
 * 主要功能：
 * - 站点配置管理（多语言支持）
 * - 域名配置管理
 * - API配置管理
 * - 公开API配置获取
 * 
 * <AUTHOR> Blog System
 * @version 1.0.0
 */

const ConfigModel = require('../models/config');
const { ResponseUtil, asyncHandler, formatValidationErrors } = require('../utils/response');
const { validationResult } = require('express-validator');

/**
 * 配置控制器类
 */
class ConfigController {
    
    // ==================== 公开API ====================
    
    /**
     * 获取站点配置（前端使用）
     * GET /public/site-config
     * 
     * Query参数:
     * - language: 语言代码 (必需)
     * 
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static getPublicSiteConfig = asyncHandler(async (req, res) => {
        try {
            const { language } = req.query;
            
            // 验证必需参数
            if (!language) {
                return ResponseUtil.badRequest(res, '语言代码参数不能为空');
            }
            
            // 验证语言代码
            const validLanguages = ['en', 'de', 'ru', 'zh'];
            if (!validLanguages.includes(language)) {
                return ResponseUtil.badRequest(res, '不支持的语言代码');
            }
            
            // 获取站点配置
            const siteConfig = await ConfigModel.getSiteConfig(language);
            
            if (!siteConfig) {
                // 如果指定语言的配置不存在，返回默认配置
                const defaultConfig = {
                    site_name: language === 'en' ? 'Pet Care Blog' : 
                              language === 'de' ? 'Haustier-Pflegeblog' :
                              language === 'ru' ? 'Блог о домашних животных' : '宠物护理博客',
                    site_description: '',
                    google_analytics_code: '',
                    google_adsense_code: '',
                    ads_enabled: false
                };
                
                return ResponseUtil.success(res, defaultConfig, '获取默认站点配置成功');
            }
            
            // 只返回公开的配置信息（不包含敏感信息）
            const publicConfig = {
                site_name: siteConfig.site_name || '',
                site_description: siteConfig.site_description || '',
                google_analytics_code: siteConfig.google_analytics_code || '',
                google_adsense_code: siteConfig.google_adsense_code || '',
                ads_enabled: Boolean(siteConfig.ads_enabled)
            };
            
            return ResponseUtil.success(res, publicConfig, '获取站点配置成功');
            
        } catch (error) {
            console.error('获取公开站点配置失败:', error);
            return ResponseUtil.serverError(res, '获取站点配置失败，请稍后重试', error);
        }
    });
    
    // ==================== 管理后台API ====================
    
    /**
     * 更新站点配置
     * PUT /admin/site-config/:language
     * 
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static updateSiteConfig = asyncHandler(async (req, res) => {
        try {
            // 检查参数验证结果
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return ResponseUtil.validationError(
                    res, 
                    formatValidationErrors(errors.array()), 
                    '站点配置参数验证失败'
                );
            }
            
            const { language } = req.params;
            const {
                site_name,
                site_description,
                google_analytics_code,
                google_adsense_code,
                ads_enabled
            } = req.body;
            
            // 验证语言代码
            const validLanguages = ['en', 'de', 'ru', 'zh'];
            if (!validLanguages.includes(language)) {
                return ResponseUtil.badRequest(res, '不支持的语言代码');
            }
            
            // 构建配置数据
            const configData = {
                site_name,
                site_description,
                google_analytics_code,
                google_adsense_code,
                ads_enabled: Boolean(ads_enabled)
            };
            
            // 更新站点配置
            const updatedConfig = await ConfigModel.updateSiteConfig(language, configData);
            
            // 记录操作日志
            console.log(`站点配置更新成功: 语言=${language} by admin ${req.user?.username} at ${new Date().toISOString()}`);
            
            return ResponseUtil.success(res, updatedConfig, '站点配置更新成功');
            
        } catch (error) {
            console.error('更新站点配置失败:', error);
            return ResponseUtil.serverError(res, '站点配置更新失败，请稍后重试', error);
        }
    });
    
    /**
     * 获取所有站点配置（管理后台）
     * GET /admin/site-configs
     * 
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static getAllSiteConfigs = asyncHandler(async (req, res) => {
        try {
            const configs = await ConfigModel.getAllSiteConfigs();
            
            return ResponseUtil.success(res, {
                configs,
                total: configs.length
            }, '获取站点配置列表成功');
            
        } catch (error) {
            console.error('获取站点配置列表失败:', error);
            return ResponseUtil.serverError(res, '获取站点配置列表失败，请稍后重试', error);
        }
    });
    
    /**
     * 添加域名配置
     * POST /admin/domains
     * 
     * Body参数:
     * - domain: 域名
     * - language: 语言代码
     * - is_active: 是否激活
     * 
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static addDomain = asyncHandler(async (req, res) => {
        try {
            // 检查参数验证结果
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return ResponseUtil.validationError(
                    res, 
                    formatValidationErrors(errors.array()), 
                    '域名参数验证失败'
                );
            }
            
            const { domain, language, is_active = true } = req.body;
            
            // 验证必需参数
            if (!domain || !language) {
                return ResponseUtil.badRequest(res, '域名和语言代码不能为空');
            }
            
            // 验证语言代码
            const validLanguages = ['en', 'de', 'ru', 'zh'];
            if (!validLanguages.includes(language)) {
                return ResponseUtil.badRequest(res, '不支持的语言代码');
            }
            
            // 验证域名格式
            const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
            if (!domainRegex.test(domain)) {
                return ResponseUtil.badRequest(res, '域名格式不正确');
            }
            
            // 添加域名配置
            const newDomain = await ConfigModel.addDomain({
                domain,
                language,
                is_active: Boolean(is_active)
            });
            
            // 记录操作日志
            console.log(`域名配置创建成功: ${domain} -> ${language} by admin ${req.user?.username} at ${new Date().toISOString()}`);
            
            return ResponseUtil.success(res, newDomain, '域名配置创建成功');
            
        } catch (error) {
            console.error('添加域名配置失败:', error);
            
            // 处理特定错误
            if (error.message.includes('域名已存在')) {
                return ResponseUtil.badRequest(res, error.message);
            }
            
            return ResponseUtil.serverError(res, '添加域名配置失败，请稍后重试', error);
        }
    });
    
    /**
     * 更新域名配置
     * PUT /admin/domains/:id
     * 
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static updateDomain = asyncHandler(async (req, res) => {
        try {
            // 检查参数验证结果
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return ResponseUtil.validationError(
                    res, 
                    formatValidationErrors(errors.array()), 
                    '域名参数验证失败'
                );
            }
            
            const domainId = parseInt(req.params.id);
            const { domain, language, is_active } = req.body;
            
            if (!domainId || isNaN(domainId)) {
                return ResponseUtil.badRequest(res, '无效的域名配置ID');
            }
            
            // 验证语言代码
            if (language) {
                const validLanguages = ['en', 'de', 'ru', 'zh'];
                if (!validLanguages.includes(language)) {
                    return ResponseUtil.badRequest(res, '不支持的语言代码');
                }
            }
            
            // 验证域名格式
            if (domain) {
                const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
                if (!domainRegex.test(domain)) {
                    return ResponseUtil.badRequest(res, '域名格式不正确');
                }
            }
            
            // 更新域名配置
            const updatedDomain = await ConfigModel.updateDomain(domainId, {
                domain,
                language,
                is_active: is_active !== undefined ? Boolean(is_active) : undefined
            });
            
            // 记录操作日志
            console.log(`域名配置更新成功: ID=${domainId} by admin ${req.user?.username} at ${new Date().toISOString()}`);
            
            return ResponseUtil.success(res, updatedDomain, '域名配置更新成功');
            
        } catch (error) {
            console.error('更新域名配置失败:', error);
            
            // 处理特定错误
            if (error.message.includes('域名配置不存在')) {
                return ResponseUtil.notFound(res, error.message);
            }
            if (error.message.includes('域名已存在')) {
                return ResponseUtil.badRequest(res, error.message);
            }
            
            return ResponseUtil.serverError(res, '更新域名配置失败，请稍后重试', error);
        }
    });
    
    /**
     * 删除域名配置
     * DELETE /admin/domains/:id
     * 
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static deleteDomain = asyncHandler(async (req, res) => {
        try {
            const domainId = parseInt(req.params.id);
            
            if (!domainId || isNaN(domainId)) {
                return ResponseUtil.badRequest(res, '无效的域名配置ID');
            }
            
            // 删除域名配置
            const success = await ConfigModel.deleteDomain(domainId);
            
            if (!success) {
                return ResponseUtil.serverError(res, '域名配置删除失败');
            }
            
            // 记录操作日志
            console.log(`域名配置删除成功: ID=${domainId} by admin ${req.user?.username} at ${new Date().toISOString()}`);
            
            return ResponseUtil.success(res, null, '域名配置删除成功');
            
        } catch (error) {
            console.error('删除域名配置失败:', error);
            
            // 处理特定错误
            if (error.message.includes('域名配置不存在')) {
                return ResponseUtil.notFound(res, error.message);
            }
            
            return ResponseUtil.serverError(res, '删除域名配置失败，请稍后重试', error);
        }
    });
    
    /**
     * 获取域名配置列表
     * GET /admin/domains
     * 
     * Query参数:
     * - language: 语言筛选（可选）
     * 
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static getAllDomains = asyncHandler(async (req, res) => {
        try {
            const { language } = req.query;
            
            // 验证语言代码
            if (language) {
                const validLanguages = ['en', 'de', 'ru', 'zh'];
                if (!validLanguages.includes(language)) {
                    return ResponseUtil.badRequest(res, '不支持的语言代码');
                }
            }
            
            const domains = await ConfigModel.getAllDomains(language);
            
            return ResponseUtil.success(res, {
                domains,
                total: domains.length
            }, '获取域名配置列表成功');
            
        } catch (error) {
            console.error('获取域名配置列表失败:', error);
            return ResponseUtil.serverError(res, '获取域名配置列表失败，请稍后重试', error);
        }
    });
    
    /**
     * 更新API配置
     * PUT /admin/api-config
     * 
     * Body参数:
     * - api_url: API地址
     * - api_key: API密钥
     * - model_name: 模型名称
     * 
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static updateApiConfig = asyncHandler(async (req, res) => {
        try {
            // 检查参数验证结果
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return ResponseUtil.validationError(
                    res, 
                    formatValidationErrors(errors.array()), 
                    'API配置参数验证失败'
                );
            }
            
            const { api_url, api_key, model_name } = req.body;
            
            // 验证必需参数
            if (!api_url || !api_key) {
                return ResponseUtil.badRequest(res, 'API地址和密钥不能为空');
            }
            
            // 验证URL格式
            try {
                new URL(api_url);
            } catch (urlError) {
                return ResponseUtil.badRequest(res, 'API地址格式不正确');
            }
            
            // 更新API配置
            const updatedConfig = await ConfigModel.updateApiConfig({
                api_url,
                api_key,
                model_name
            });
            
            // 记录操作日志（不记录敏感信息）
            console.log(`API配置更新成功: URL=${api_url.replace(/\/\/.+@/, '//***@')} by admin ${req.user?.username} at ${new Date().toISOString()}`);
            
            // 返回结果时隐藏敏感信息
            const responseConfig = {
                ...updatedConfig,
                api_key: '***' // 隐藏API密钥
            };
            
            return ResponseUtil.success(res, responseConfig, 'API配置更新成功');
            
        } catch (error) {
            console.error('更新API配置失败:', error);
            return ResponseUtil.serverError(res, 'API配置更新失败，请稍后重试', error);
        }
    });
    
    /**
     * 获取API配置列表
     * GET /admin/api-configs
     * 
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static getAllApiConfigs = asyncHandler(async (req, res) => {
        try {
            const configs = await ConfigModel.getAllApiConfigs();
            
            // 隐藏敏感信息
            const safeConfigs = configs.map(config => ({
                ...config,
                api_key: '***' // 隐藏API密钥
            }));
            
            return ResponseUtil.success(res, {
                configs: safeConfigs,
                total: safeConfigs.length
            }, '获取API配置列表成功');
            
        } catch (error) {
            console.error('获取API配置列表失败:', error);
            return ResponseUtil.serverError(res, '获取API配置列表失败，请稍后重试', error);
        }
    });
    
    /**
     * 获取激活的API配置
     * GET /admin/api-config/active
     * 
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static getActiveApiConfig = asyncHandler(async (req, res) => {
        try {
            const activeConfig = await ConfigModel.getActiveApiConfig();
            
            if (!activeConfig) {
                return ResponseUtil.notFound(res, '没有激活的API配置');
            }
            
            // 隐藏敏感信息
            const safeConfig = {
                ...activeConfig,
                api_key: '***' // 隐藏API密钥
            };
            
            return ResponseUtil.success(res, safeConfig, '获取激活API配置成功');
            
        } catch (error) {
            console.error('获取激活API配置失败:', error);
            return ResponseUtil.serverError(res, '获取激活API配置失败，请稍后重试', error);
        }
    });
}

module.exports = ConfigController;