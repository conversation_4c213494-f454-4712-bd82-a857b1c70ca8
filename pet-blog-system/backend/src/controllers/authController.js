/**
 * 认证控制器
 * 
 * 处理管理员登录、认证、JWT Token生成和刷新等功能
 * 
 * 主要功能：
 * - 管理员登录验证
 * - JWT Token生成和验证
 * - Refresh Token处理
 * - 登录状态管理
 * 
 * <AUTHOR> Blog System
 * @version 1.0.0
 */

const AdminModel = require('../models/admin');
const { ResponseUtil, asyncHandler, formatValidationErrors } = require('../utils/response');
const { validationResult } = require('express-validator');
const {
    generateAccessToken,
    generateRefreshToken,
    verifyToken,
    extractBearerToken
} = require('../middleware/auth');

/**
 * 认证控制器类
 */
class AuthController {
    
    /**
     * 管理员登录
     * POST /admin/auth/login
     * 
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static login = asyncHandler(async (req, res) => {
        try {
            // 检查参数验证结果
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return ResponseUtil.validationError(
                    res, 
                    formatValidationErrors(errors.array()), 
                    '登录参数验证失败'
                );
            }
            
            const { username, password } = req.body;
            
            // 参数基本检查
            if (!username || !password) {
                return ResponseUtil.badRequest(res, '用户名和密码不能为空');
            }
            
            // 验证管理员身份
            const admin = await AdminModel.authenticate(username, password);
            if (!admin) {
                // 出于安全考虑，不区分用户名不存在还是密码错误
                return ResponseUtil.unauthorized(res, '用户名或密码错误');
            }
            
            // 生成tokens (使用认证中间件的工具函数)
            const accessToken = generateAccessToken(admin);
            const refreshToken = generateRefreshToken(admin);
            
            // 构建响应数据
            const responseData = {
                token: accessToken,
                refresh_token: refreshToken,
                expires_in: process.env.JWT_EXPIRE || '24h',
                token_type: 'Bearer',
                user: {
                    id: admin.id,
                    username: admin.username,
                    last_login: admin.last_login
                }
            };
            
            // 记录登录成功日志
            console.log(`管理员登录成功: ${username} (ID: ${admin.id}) at ${new Date().toISOString()}`);
            
            return ResponseUtil.success(res, responseData, '登录成功');
            
        } catch (error) {
            console.error('管理员登录失败:', error);
            return ResponseUtil.serverError(res, '登录失败，请稍后重试', error);
        }
    });
    
    /**
     * 刷新Access Token
     * POST /admin/auth/refresh
     * 
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static refresh = asyncHandler(async (req, res) => {
        try {
            const refreshToken = extractBearerToken(req) || req.body.refresh_token;
            
            if (!refreshToken) {
                return ResponseUtil.badRequest(res, 'Refresh Token缺失');
            }
            
            // 验证refresh token (使用认证中间件的工具函数)
            let decoded;
            try {
                decoded = verifyToken(refreshToken);
                if (!decoded || decoded.type !== 'refresh') {
                    return ResponseUtil.unauthorized(res, 'Refresh Token无效或类型错误');
                }
            } catch (error) {
                return ResponseUtil.unauthorized(res, 'Refresh Token无效或已过期');
            }
            
            // 验证管理员是否仍然存在
            const admin = await AdminModel.findById(decoded.id);
            if (!admin) {
                return ResponseUtil.unauthorized(res, '管理员账号不存在');
            }
            
            // 生成新的tokens (使用认证中间件的工具函数)
            const newAccessToken = generateAccessToken(admin);
            const newRefreshToken = generateRefreshToken(admin);
            
            const responseData = {
                token: newAccessToken,
                refresh_token: newRefreshToken,
                expires_in: process.env.JWT_EXPIRE || '24h',
                token_type: 'Bearer',
                user: {
                    id: admin.id,
                    username: admin.username,
                    last_login: admin.last_login
                }
            };
            
            console.log(`Token刷新成功: ${admin.username} (ID: ${admin.id}) at ${new Date().toISOString()}`);
            
            return ResponseUtil.success(res, responseData, 'Token刷新成功');
            
        } catch (error) {
            console.error('Token刷新失败:', error);
            return ResponseUtil.serverError(res, 'Token刷新失败', error);
        }
    });
    
    /**
     * 验证当前用户信息
     * GET /admin/auth/me
     * 需要在请求头中包含有效的Access Token
     * 
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static me = asyncHandler(async (req, res) => {
        try {
            // 从中间件中获取用户信息（需要先通过auth中间件）
            const adminId = req.user?.id;
            
            if (!adminId) {
                return ResponseUtil.unauthorized(res, '未找到用户信息');
            }
            
            // 获取最新的管理员信息
            const admin = await AdminModel.findById(adminId);
            if (!admin) {
                return ResponseUtil.unauthorized(res, '管理员账号不存在');
            }
            
            const responseData = {
                user: {
                    id: admin.id,
                    username: admin.username,
                    last_login: admin.last_login,
                    created_at: admin.created_at,
                    updated_at: admin.updated_at
                }
            };
            
            return ResponseUtil.success(res, responseData, '获取用户信息成功');
            
        } catch (error) {
            console.error('获取用户信息失败:', error);
            return ResponseUtil.serverError(res, '获取用户信息失败', error);
        }
    });
    
    /**
     * 管理员登出
     * POST /admin/auth/logout
     * 
     * 注意：JWT是无状态的，真正的登出需要在客户端删除token
     * 这里主要做日志记录，如果需要真正的token失效，可以考虑黑名单机制
     * 
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static logout = asyncHandler(async (req, res) => {
        try {
            const adminId = req.user?.id;
            const username = req.user?.username;
            
            // 记录登出日志
            if (adminId && username) {
                console.log(`管理员登出: ${username} (ID: ${adminId}) at ${new Date().toISOString()}`);
            }
            
            // 如果需要实现token黑名单，可以在这里添加逻辑
            // 例如：将token添加到Redis黑名单中
            
            return ResponseUtil.success(res, null, '登出成功');
            
        } catch (error) {
            console.error('管理员登出失败:', error);
            return ResponseUtil.serverError(res, '登出失败', error);
        }
    });

    /**
     * 验证Token有效性
     * GET /admin/auth/verify
     *
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static verify = asyncHandler(async (req, res) => {
        try {
            // 如果能到达这里，说明token验证已经通过（requireAuth中间件）
            const user = req.user;

            if (!user) {
                return ResponseUtil.unauthorized(res, 'Token验证失败');
            }

            return ResponseUtil.success(res, {
                valid: true,
                user: {
                    id: user.id,
                    username: user.username
                },
                token_info: {
                    type: 'access',
                    verified_at: new Date().toISOString()
                }
            }, 'Token验证成功');

        } catch (error) {
            console.error('Token验证失败:', error);
            return ResponseUtil.unauthorized(res, 'Token验证失败');
        }
    });

    /**
     * 修改密码
     * POST /admin/auth/change-password
     * 
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static changePassword = asyncHandler(async (req, res) => {
        try {
            // 检查参数验证结果
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return ResponseUtil.validationError(
                    res, 
                    errors.array(), 
                    '参数验证失败'
                );
            }
            
            const { current_password, new_password } = req.body;
            const adminId = req.user?.id;
            const username = req.user?.username;
            
            if (!current_password || !new_password) {
                return ResponseUtil.badRequest(res, '当前密码和新密码不能为空');
            }
            
            // 验证当前密码
            const admin = await AdminModel.authenticate(username, current_password);
            if (!admin) {
                return ResponseUtil.badRequest(res, '当前密码错误');
            }
            
            // 验证新密码强度
            const passwordValidation = AdminModel.validatePasswordStrength(new_password);
            if (!passwordValidation.isValid) {
                return ResponseUtil.badRequest(res, '新密码强度不足', {
                    errors: passwordValidation.errors
                });
            }
            
            // 更新密码
            const success = await AdminModel.updatePassword(adminId, new_password);
            if (!success) {
                return ResponseUtil.serverError(res, '密码修改失败');
            }
            
            console.log(`管理员修改密码: ${username} (ID: ${adminId}) at ${new Date().toISOString()}`);
            
            return ResponseUtil.success(res, null, '密码修改成功');
            
        } catch (error) {
            console.error('修改密码失败:', error);
            return ResponseUtil.serverError(res, '密码修改失败', error);
        }
    });
}

module.exports = AuthController;