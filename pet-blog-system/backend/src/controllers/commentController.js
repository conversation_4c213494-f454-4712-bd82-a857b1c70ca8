/**
 * 评论控制器
 * 
 * 处理评论系统的完整功能，包括：
 * - 公开API：评论获取、评论提交（前端使用）
 * - 管理API：评论审核、批量操作、统计查询（后台管理）
 * - 多语言支持和嵌套评论结构
 * - 完善的安全过滤和参数验证
 * - 防垃圾评论和IP封禁检查
 * 
 * <AUTHOR> Blog System
 * @version 1.0.0
 * @date 2025-01-09
 */

const CommentModel = require('../models/comment');
const { ResponseUtil, asyncHandler, formatValidationErrors } = require('../utils/response');
const { validationResult } = require('express-validator');
const sanitizeHtml = require('sanitize-html');

/**
 * 评论控制器类
 */
class CommentController {
    
    // ==================== 公开API（前端使用） ====================
    
    /**
     * 获取文章评论列表（前端使用）
     * GET /public/comments
     * 
     * Query参数:
     * - article_id: 文章ID (必需)
     * - language: 语言代码 (必需)
     * - page: 页码 (默认1)
     * - limit: 每页数量 (默认20, 最大50)
     * 
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static getArticleComments = asyncHandler(async (req, res) => {
        try {
            const { article_id, language, page = 1, limit = 20 } = req.query;
            
            // 验证必需参数
            if (!article_id || !language) {
                return ResponseUtil.badRequest(res, '文章ID和语言代码不能为空');
            }
            
            // 验证参数格式
            const articleId = parseInt(article_id);
            if (isNaN(articleId) || articleId <= 0) {
                return ResponseUtil.badRequest(res, '文章ID格式无效');
            }
            
            // 验证语言代码
            const validLanguages = ['en', 'de', 'ru', 'zh'];
            if (!validLanguages.includes(language)) {
                return ResponseUtil.badRequest(res, '不支持的语言代码');
            }
            
            // 验证分页参数
            const pageNum = Math.max(1, parseInt(page));
            const limitNum = Math.min(50, Math.max(1, parseInt(limit)));
            
            // 获取评论列表
            const result = await CommentModel.findByArticle(articleId, language, {
                page: pageNum,
                limit: limitNum,
                status: 'approved', // 只显示已审核的评论
                includeReplies: true
            });
            
            // 格式化响应数据，移除敏感信息
            const formattedComments = result.comments.map(comment => ({
                id: comment.id,
                username: comment.username,
                content: comment.content,
                created_at: comment.created_at,
                replies: comment.replies ? comment.replies.map(reply => ({
                    id: reply.id,
                    username: reply.username,
                    content: reply.content,
                    created_at: reply.created_at
                })) : []
            }));
            
            console.log(`获取文章评论: 文章ID=${articleId}, 语言=${language}, 评论数=${formattedComments.length}`);
            
            return ResponseUtil.success(res, {
                comments: formattedComments,
                pagination: result.pagination
            }, '获取评论列表成功');
            
        } catch (error) {
            console.error('获取文章评论失败:', error);
            return ResponseUtil.serverError(res, '获取评论失败，请稍后重试', error);
        }
    });
    
    /**
     * 提交新评论（前端使用）
     * POST /public/comments
     * 
     * Body参数:
     * - article_id: 文章ID (必需)
     * - language: 语言代码 (必需)
     * - parent_id: 父评论ID (可选)
     * - username: 用户名 (必需)
     * - email: 邮箱 (必需)
     * - content: 评论内容 (必需)
     * 
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static submitComment = asyncHandler(async (req, res) => {
        try {
            const { article_id, language, parent_id, username, email, content } = req.body;
            
            // 验证必需参数
            if (!article_id || !language || !username || !email || !content) {
                return ResponseUtil.badRequest(res, '文章ID、语言代码、用户名、邮箱和评论内容不能为空');
            }
            
            // 验证参数格式
            const articleId = parseInt(article_id);
            if (isNaN(articleId) || articleId <= 0) {
                return ResponseUtil.badRequest(res, '文章ID格式无效');
            }
            
            // 验证语言代码
            const validLanguages = ['en', 'de', 'ru', 'zh'];
            if (!validLanguages.includes(language)) {
                return ResponseUtil.badRequest(res, '不支持的语言代码');
            }
            
            // 验证用户名长度
            if (username.length < 2 || username.length > 50) {
                return ResponseUtil.badRequest(res, '用户名长度必须在2-50字符之间');
            }
            
            // 验证邮箱格式
            if (!CommentModel.validateEmail(email)) {
                return ResponseUtil.badRequest(res, '邮箱格式无效');
            }
            
            // 验证评论内容长度
            if (content.length < 5 || content.length > 2000) {
                return ResponseUtil.badRequest(res, '评论内容长度必须在5-2000字符之间');
            }
            
            // 获取用户IP地址
            const ipAddress = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'];
            const userAgent = req.headers['user-agent'] || '';
            
            // 检查用户是否被封禁
            const isBanned = await CommentModel.isUserBanned(ipAddress, email);
            if (isBanned) {
                return ResponseUtil.forbidden(res, '您已被限制评论，请联系管理员');
            }
            
            // 验证父评论（如果有）
            let parentIdNum = null;
            if (parent_id) {
                parentIdNum = parseInt(parent_id);
                if (isNaN(parentIdNum) || parentIdNum <= 0) {
                    return ResponseUtil.badRequest(res, '父评论ID格式无效');
                }
            }
            
            // 构建评论数据
            const commentData = {
                article_id: articleId,
                language,
                parent_id: parentIdNum,
                username: username.trim(),
                email: email.trim().toLowerCase(),
                content: content.trim(),
                ip_address: ipAddress,
                user_agent: userAgent
            };
            
            // 创建评论
            const newComment = await CommentModel.create(commentData);
            
            console.log(`新评论提交: ID=${newComment.id}, 文章ID=${articleId}, 用户=${username}, IP=${ipAddress}`);
            
            return ResponseUtil.success(res, {
                comment: {
                    id: newComment.id,
                    username: newComment.username,
                    content: newComment.content,
                    status: newComment.status,
                    created_at: newComment.created_at
                }
            }, '评论提交成功，等待管理员审核');
            
        } catch (error) {
            console.error('提交评论失败:', error);
            
            // 处理特定错误
            if (error.message.includes('文章不存在')) {
                return ResponseUtil.notFound(res, '文章不存在');
            }
            if (error.message.includes('父评论不存在')) {
                return ResponseUtil.badRequest(res, '回复的评论不存在或未审核');
            }
            
            return ResponseUtil.serverError(res, '提交评论失败，请稍后重试', error);
        }
    });
    
    // ==================== 管理API（后台使用，需要JWT认证） ====================
    
    /**
     * 获取评论列表（管理后台使用）
     * GET /admin/comments
     * 
     * Query参数:
     * - status: 状态筛选 (pending/approved/rejected)
     * - language: 语言筛选
     * - keyword: 搜索关键词
     * - page: 页码 (默认1)
     * - limit: 每页数量 (默认50)
     * 
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static getAdminComments = asyncHandler(async (req, res) => {
        try {
            const { status, language, keyword, page = 1, limit = 50 } = req.query;
            const adminUser = req.user; // 从JWT认证中间件获取
            
            // 验证分页参数
            const pageNum = Math.max(1, parseInt(page));
            const limitNum = Math.min(200, Math.max(1, parseInt(limit)));
            
            // 构建搜索条件
            const criteria = {};
            
            if (status && ['pending', 'approved', 'rejected'].includes(status)) {
                criteria.status = status;
            }
            
            if (language && ['en', 'de', 'ru', 'zh'].includes(language)) {
                criteria.language = language;
            }
            
            if (keyword && keyword.trim()) {
                criteria.keyword = keyword.trim();
            }
            
            // 搜索评论
            const result = await CommentModel.search(criteria, {
                page: pageNum,
                limit: limitNum
            });
            
            // 格式化响应数据
            const formattedComments = result.comments.map(comment => ({
                id: comment.id,
                article_id: comment.article_id,
                article_title: comment.article_title || '未知文章',
                language: comment.language,
                parent_id: comment.parent_id,
                username: comment.username,
                email: comment.email,
                content: comment.content,
                status: comment.status,
                ip_address: comment.ip_address,
                created_at: comment.created_at,
                updated_at: comment.updated_at
            }));
            
            console.log(`管理员获取评论列表: 用户=${adminUser.username}, 状态筛选=${status || '全部'}, 数量=${formattedComments.length}`);
            
            return ResponseUtil.success(res, {
                comments: formattedComments,
                pagination: result.pagination
            }, '获取评论列表成功');
            
        } catch (error) {
            console.error('获取管理评论列表失败:', error);
            return ResponseUtil.serverError(res, '获取评论列表失败，请稍后重试', error);
        }
    });
    
    /**
     * 获取待审核评论列表
     * GET /admin/comments/pending
     * 
     * Query参数:
     * - page: 页码 (默认1)  
     * - limit: 每页数量 (默认50)
     * 
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static getPendingComments = asyncHandler(async (req, res) => {
        try {
            const { page = 1, limit = 50 } = req.query;
            const adminUser = req.user;
            
            // 验证分页参数
            const pageNum = Math.max(1, parseInt(page));
            const limitNum = Math.min(200, Math.max(1, parseInt(limit)));
            
            // 获取待审核评论
            const result = await CommentModel.findPending({
                page: pageNum,
                limit: limitNum
            });
            
            console.log(`获取待审核评论: 管理员=${adminUser.username}, 数量=${result.comments.length}`);
            
            return ResponseUtil.success(res, {
                comments: result.comments,
                pagination: result.pagination
            }, '获取待审核评论成功');
            
        } catch (error) {
            console.error('获取待审核评论失败:', error);
            return ResponseUtil.serverError(res, '获取待审核评论失败，请稍后重试', error);
        }
    });
    
    /**
     * 审核单个评论
     * PUT /admin/comments/:id/status
     * 
     * Body参数:
     * - status: 新状态 (approved/rejected/pending)
     * 
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static updateCommentStatus = asyncHandler(async (req, res) => {
        try {
            const { id } = req.params;
            const { status } = req.body;
            const adminUser = req.user;
            
            // 验证评论ID
            const commentId = parseInt(id);
            if (isNaN(commentId) || commentId <= 0) {
                return ResponseUtil.badRequest(res, '评论ID格式无效');
            }
            
            // 验证状态参数
            const validStatuses = ['pending', 'approved', 'rejected'];
            if (!status || !validStatuses.includes(status)) {
                return ResponseUtil.badRequest(res, '状态参数无效，只能是: pending, approved, rejected');
            }
            
            // 检查评论是否存在
            const comment = await CommentModel.findById(commentId);
            if (!comment) {
                return ResponseUtil.notFound(res, '评论不存在');
            }
            
            // 更新评论状态
            const updated = await CommentModel.updateStatus(commentId, status);
            
            if (updated) {
                console.log(`评论状态更新: ID=${commentId}, 新状态=${status}, 管理员=${adminUser.username}`);
                
                return ResponseUtil.success(res, {
                    comment_id: commentId,
                    old_status: comment.status,
                    new_status: status,
                    updated_at: new Date()
                }, '评论状态更新成功');
            } else {
                return ResponseUtil.serverError(res, '更新评论状态失败');
            }
            
        } catch (error) {
            console.error('更新评论状态失败:', error);
            return ResponseUtil.serverError(res, '更新评论状态失败，请稍后重试', error);
        }
    });
    
    /**
     * 批量审核评论
     * POST /admin/comments/batch-approve
     * 
     * Body参数:
     * - ids: 评论ID数组
     * - status: 要设置的状态 (默认approved)
     * 
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static batchUpdateComments = asyncHandler(async (req, res) => {
        try {
            const { ids, status = 'approved' } = req.body;
            const adminUser = req.user;
            
            // 验证参数
            if (!ids || !Array.isArray(ids) || ids.length === 0) {
                return ResponseUtil.badRequest(res, '评论ID列表不能为空');
            }
            
            // 验证状态参数
            const validStatuses = ['pending', 'approved', 'rejected'];
            if (!validStatuses.includes(status)) {
                return ResponseUtil.badRequest(res, '状态参数无效');
            }
            
            // 验证ID格式并去重
            const commentIds = [...new Set(ids.map(id => parseInt(id)).filter(id => !isNaN(id) && id > 0))];
            
            if (commentIds.length === 0) {
                return ResponseUtil.badRequest(res, '没有有效的评论ID');
            }
            
            if (commentIds.length > 100) {
                return ResponseUtil.badRequest(res, '批量操作最多支持100条记录');
            }
            
            // 执行批量更新
            const updatedCount = await CommentModel.updateStatusBatch(commentIds, status);
            
            console.log(`批量更新评论状态: 数量=${updatedCount}/${commentIds.length}, 状态=${status}, 管理员=${adminUser.username}`);
            
            return ResponseUtil.success(res, {
                requested_count: commentIds.length,
                updated_count: updatedCount,
                status: status,
                comment_ids: commentIds
            }, `批量更新成功，共更新${updatedCount}条评论`);
            
        } catch (error) {
            console.error('批量更新评论状态失败:', error);
            return ResponseUtil.serverError(res, '批量更新失败，请稍后重试', error);
        }
    });
    
    /**
     * 删除评论（软删除）
     * DELETE /admin/comments/:id
     * 
     * Query参数:
     * - force: 是否强制删除 (true/false, 默认false)
     * 
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static deleteComment = asyncHandler(async (req, res) => {
        try {
            const { id } = req.params;
            const { force = false } = req.query;
            const adminUser = req.user;
            
            // 验证评论ID
            const commentId = parseInt(id);
            if (isNaN(commentId) || commentId <= 0) {
                return ResponseUtil.badRequest(res, '评论ID格式无效');
            }
            
            // 检查评论是否存在
            const comment = await CommentModel.findById(commentId);
            if (!comment) {
                return ResponseUtil.notFound(res, '评论不存在');
            }
            
            let deleted = false;
            const forceDelete = force === 'true' || force === true;
            
            if (forceDelete) {
                // 硬删除（物理删除）
                deleted = await CommentModel.hardDelete(commentId);
                console.log(`评论硬删除: ID=${commentId}, 管理员=${adminUser.username}`);
            } else {
                // 软删除（设置状态为rejected）
                deleted = await CommentModel.delete(commentId);
                console.log(`评论软删除: ID=${commentId}, 管理员=${adminUser.username}`);
            }
            
            if (deleted) {
                return ResponseUtil.success(res, {
                    comment_id: commentId,
                    delete_type: forceDelete ? 'hard' : 'soft',
                    deleted_at: new Date()
                }, forceDelete ? '评论已彻底删除' : '评论已删除');
            } else {
                return ResponseUtil.serverError(res, '删除评论失败');
            }
            
        } catch (error) {
            console.error('删除评论失败:', error);
            return ResponseUtil.serverError(res, '删除评论失败，请稍后重试', error);
        }
    });

    /**
     * 获取待审核评论数量
     * GET /admin/comments/pending-count
     *
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static getPendingCount = asyncHandler(async (req, res) => {
        try {
            const adminUser = req.user;

            // 获取待审核评论数量
            const count = await CommentModel.getPendingCount();

            console.log(`管理员 ${adminUser.username} 查询待审核评论数量: ${count}`);

            return ResponseUtil.success(res, {
                pending_count: count,
                checked_at: new Date().toISOString()
            }, '获取待审核评论数量成功');

        } catch (error) {
            console.error('获取待审核评论数量失败:', error);
            return ResponseUtil.serverError(res, '获取待审核评论数量失败，请稍后重试', error);
        }
    });

    /**
     * 获取评论统计信息
     * GET /admin/comments/stats
     * 
     * Query参数:
     * - article_id: 特定文章的统计 (可选)
     * 
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    static getCommentStats = asyncHandler(async (req, res) => {
        try {
            const { article_id } = req.query;
            const adminUser = req.user;
            
            let articleId = null;
            if (article_id) {
                articleId = parseInt(article_id);
                if (isNaN(articleId) || articleId <= 0) {
                    return ResponseUtil.badRequest(res, '文章ID格式无效');
                }
            }
            
            // 获取统计信息
            const stats = await CommentModel.getStats(articleId);
            
            console.log(`获取评论统计: 管理员=${adminUser.username}, 文章ID=${articleId || '全部'}`);
            
            return ResponseUtil.success(res, {
                stats: {
                    total: stats.total,
                    pending: stats.pending,
                    approved: stats.approved,
                    rejected: stats.rejected,
                    approval_rate: stats.total > 0 ? ((stats.approved / stats.total) * 100).toFixed(2) + '%' : '0%'
                },
                scope: articleId ? `文章${articleId}` : '全部文章'
            }, '获取统计信息成功');
            
        } catch (error) {
            console.error('获取评论统计失败:', error);
            return ResponseUtil.serverError(res, '获取统计信息失败，请稍后重试', error);
        }
    });
    
    // ==================== 辅助方法 ====================
    
    /**
     * 验证评论内容（内部使用）
     * @param {string} content - 评论内容
     * @returns {Object} 验证结果
     */
    static validateCommentContent(content) {
        if (!content || typeof content !== 'string') {
            return { valid: false, message: '评论内容不能为空' };
        }
        
        const trimmedContent = content.trim();
        
        if (trimmedContent.length < 5) {
            return { valid: false, message: '评论内容至少需要5个字符' };
        }
        
        if (trimmedContent.length > 2000) {
            return { valid: false, message: '评论内容不能超过2000个字符' };
        }
        
        // 检查是否全是特殊字符或空格
        if (!/[a-zA-Z0-9\u4e00-\u9fa5]/.test(trimmedContent)) {
            return { valid: false, message: '评论内容必须包含有效文字' };
        }
        
        return { valid: true, content: trimmedContent };
    }
    
    /**
     * 清理和格式化评论数据（内部使用）
     * @param {Object} comment - 原始评论数据
     * @returns {Object} 格式化后的评论数据
     */
    static formatCommentForPublic(comment) {
        return {
            id: comment.id,
            username: comment.username,
            content: comment.content,
            created_at: comment.created_at,
            replies_count: comment.replies ? comment.replies.length : 0
        };
    }
}

module.exports = CommentController;