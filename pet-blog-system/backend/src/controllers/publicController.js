/**
 * 公开API控制器
 * 
 * 提供前端站点使用的公开API，无需认证
 * 包括文章、分类、评论、搜索和站点配置功能
 * 
 * 功能列表：
 * - 文章列表和详情查看
 * - 分类层级结构获取
 * - 评论查看和提交
 * - 文章搜索功能
 * - 站点配置获取
 * - 浏览量统计
 * 
 * <AUTHOR> Blog System
 * @version 1.0.0
 */

const { ResponseUtil, asyncHandler } = require('../utils/response');
const { validationResult } = require('express-validator');
const ArticleModel = require('../models/article');
const CategoryModel = require('../models/category');
const CommentModel = require('../models/comment');
const ConfigModel = require('../models/config');

/**
 * 检查参数验证结果
 * @param {Object} req Express请求对象
 * @param {Object} res Express响应对象
 * @returns {boolean} 是否有验证错误
 */
function checkValidationErrors(req, res) {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        const errorMessages = errors.array().map(error => error.msg);
        ResponseUtil.badRequest(res, '参数验证失败', { errors: errorMessages });
        return true;
    }
    return false;
}

/**
 * 公开API控制器类
 */
class PublicController {
    
    // ==================== 文章相关 ====================
    
    /**
     * 获取文章列表
     * GET /public/articles?language=en&category_id=1&page=1&limit=10&search=keyword
     */
    static getArticles = asyncHandler(async (req, res) => {
        // 检查参数验证结果
        if (checkValidationErrors(req, res)) {
            return;
        }
        
        const { 
            language, 
            category_id: categoryId, 
            page = 1, 
            limit = 10, 
            search 
        } = req.query;
        
        // 验证分页参数
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        
        if (pageNum < 1 || limitNum < 1 || limitNum > 100) {
            return ResponseUtil.badRequest(res, '分页参数无效（页码≥1，每页数量1-100）');
        }
        
        const options = {
            language,
            page: pageNum,
            limit: limitNum
        };
        
        if (categoryId) {
            const categoryIdNum = parseInt(categoryId);
            if (isNaN(categoryIdNum)) {
                return ResponseUtil.badRequest(res, '分类ID必须为数字');
            }
            options.categoryId = categoryIdNum;
        }
        
        if (search && search.trim()) {
            options.search = search.trim();
        }
        
        const result = await ArticleModel.getPublicList(options);
        
        return ResponseUtil.success(res, result, '获取文章列表成功');
    });
    
    /**
     * 获取文章详情
     * GET /public/articles/:slug?language=en
     */
    static getArticleBySlug = asyncHandler(async (req, res) => {
        const { slug } = req.params;
        const { language } = req.query;
        
        // 验证参数
        if (!slug) {
            return ResponseUtil.badRequest(res, '文章别名参数必需');
        }
        
        if (!language) {
            return ResponseUtil.badRequest(res, '语言代码参数必需');
        }
        
        const article = await ArticleModel.findBySlug(slug, language, true);
        
        if (!article) {
            return ResponseUtil.notFound(res, '文章不存在');
        }
        
        // 获取相关文章（可选实现）
        try {
            // 获取同分类的其他文章作为相关文章
            if (article.categories && article.categories.length > 0) {
                const categoryId = article.categories[0].id;
                const relatedResult = await ArticleModel.getPublicList({
                    language,
                    categoryId,
                    page: 1,
                    limit: 5
                });
                
                // 排除当前文章
                article.related_articles = relatedResult.articles.filter(a => a.id !== article.id);
            } else {
                article.related_articles = [];
            }
        } catch (error) {
            console.warn('获取相关文章失败:', error);
            article.related_articles = [];
        }
        
        return ResponseUtil.success(res, article, '获取文章详情成功');
    });
    
    /**
     * 增加文章浏览量
     * POST /public/articles/:id/view
     * Body: { language: "en" }
     */
    static incrementArticleView = asyncHandler(async (req, res) => {
        const { id } = req.params;
        const { language } = req.body;
        
        // 验证参数
        const articleId = parseInt(id);
        if (isNaN(articleId)) {
            return ResponseUtil.badRequest(res, '文章ID必须为数字');
        }
        
        if (!language) {
            return ResponseUtil.badRequest(res, '语言代码参数必需');
        }
        
        // 验证文章是否存在
        const article = await ArticleModel.findById(articleId);
        if (!article) {
            return ResponseUtil.notFound(res, '文章不存在');
        }
        
        // 增加浏览量
        await ArticleModel.incrementViewCount(articleId);
        
        return ResponseUtil.success(res, null, '浏览量更新成功');
    });
    
    // ==================== 分类相关 ====================
    
    /**
     * 获取分类列表
     * GET /public/categories?language=en&type=cat
     */
    static getCategories = asyncHandler(async (req, res) => {
        const { language, type } = req.query;
        
        // 验证必需参数
        if (!language) {
            return ResponseUtil.badRequest(res, '语言代码参数必需');
        }
        
        // 验证类型参数
        if (type && !['cat', 'dog'].includes(type)) {
            return ResponseUtil.badRequest(res, '类型参数必须为cat或dog');
        }
        
        const categories = await CategoryModel.getHierarchy(type, language);
        
        return ResponseUtil.success(res, { categories }, '获取分类列表成功');
    });
    
    // ==================== 评论相关 ====================
    
    /**
     * 获取文章评论
     * GET /public/comments?article_id=1&language=en&page=1&limit=20
     */
    static getComments = asyncHandler(async (req, res) => {
        const { 
            article_id: articleId, 
            language, 
            page = 1, 
            limit = 20 
        } = req.query;
        
        // 验证必需参数
        if (!articleId) {
            return ResponseUtil.badRequest(res, '文章ID参数必需');
        }
        
        if (!language) {
            return ResponseUtil.badRequest(res, '语言代码参数必需');
        }
        
        const articleIdNum = parseInt(articleId);
        if (isNaN(articleIdNum)) {
            return ResponseUtil.badRequest(res, '文章ID必须为数字');
        }
        
        // 验证分页参数
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        
        if (pageNum < 1 || limitNum < 1 || limitNum > 50) {
            return ResponseUtil.badRequest(res, '分页参数无效（页码≥1，每页数量1-50）');
        }
        
        // 验证文章是否存在
        const article = await ArticleModel.findById(articleIdNum);
        if (!article) {
            return ResponseUtil.notFound(res, '文章不存在');
        }
        
        const result = await CommentModel.findByArticle(articleIdNum, language, {
            page: pageNum,
            limit: limitNum,
            status: 'approved',
            includeReplies: true
        });
        
        return ResponseUtil.success(res, result, '获取评论列表成功');
    });
    
    /**
     * 提交评论
     * POST /public/comments
     * Body: {
     *   article_id: number,
     *   language: string,
     *   parent_id?: number,
     *   username: string,
     *   email: string,
     *   content: string
     * }
     */
    static submitComment = asyncHandler(async (req, res) => {
        const {
            article_id: articleId,
            language,
            parent_id: parentId,
            username,
            email,
            content
        } = req.body;
        
        // 验证必需参数
        if (!articleId || !language || !username || !email || !content) {
            return ResponseUtil.badRequest(res, '缺少必需参数：article_id、language、username、email、content');
        }
        
        const articleIdNum = parseInt(articleId);
        if (isNaN(articleIdNum)) {
            return ResponseUtil.badRequest(res, '文章ID必须为数字');
        }
        
        // 验证邮箱格式
        if (!CommentModel.validateEmail(email)) {
            return ResponseUtil.badRequest(res, '邮箱格式不正确');
        }
        
        // 验证内容长度
        if (content.trim().length < 5 || content.length > 1000) {
            return ResponseUtil.badRequest(res, '评论内容长度必须在5-1000字符之间');
        }
        
        if (username.trim().length < 2 || username.length > 50) {
            return ResponseUtil.badRequest(res, '用户名长度必须在2-50字符之间');
        }
        
        // 获取客户端IP和用户代理
        const ipAddress = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'];
        const userAgent = req.get('User-Agent');
        
        // 检查用户是否被封禁
        const isBanned = await CommentModel.isUserBanned(ipAddress, email);
        if (isBanned) {
            return ResponseUtil.forbidden(res, '您已被禁止发表评论');
        }
        
        // 验证文章是否存在
        const article = await ArticleModel.findById(articleIdNum);
        if (!article) {
            return ResponseUtil.notFound(res, '文章不存在');
        }
        
        const commentData = {
            article_id: articleIdNum,
            language,
            parent_id: parentId ? parseInt(parentId) : null,
            username: username.trim(),
            email: email.trim(),
            content: content.trim(),
            ip_address: ipAddress,
            user_agent: userAgent
        };
        
        // 验证父评论ID
        if (commentData.parent_id && isNaN(commentData.parent_id)) {
            return ResponseUtil.badRequest(res, '父评论ID必须为数字');
        }
        
        const comment = await CommentModel.create(commentData);
        
        // 不返回敏感信息
        const publicComment = {
            id: comment.id,
            username: comment.username,
            content: comment.content,
            status: comment.status,
            created_at: comment.created_at
        };
        
        return ResponseUtil.created(res, publicComment, '评论提交成功，等待审核');
    });
    
    // ==================== 搜索功能 ====================
    
    /**
     * 搜索文章
     * GET /public/search?q=keyword&language=en&type=article&page=1&limit=10
     */
    static searchArticles = asyncHandler(async (req, res) => {
        // 检查参数验证结果
        if (checkValidationErrors(req, res)) {
            return;
        }
        
        const { 
            q: keyword, 
            language, 
            type = 'article',
            page = 1, 
            limit = 10 
        } = req.query;
        
        // 验证搜索类型
        if (!['article', 'category'].includes(type)) {
            return ResponseUtil.badRequest(res, '搜索类型必须为article或category');
        }
        
        // 验证分页参数
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        
        if (pageNum < 1 || limitNum < 1 || limitNum > 50) {
            return ResponseUtil.badRequest(res, '分页参数无效（页码≥1，每页数量1-50）');
        }
        
        let result;
        
        if (type === 'article') {
            // 搜索文章
            result = await ArticleModel.search({
                language,
                keyword: keyword.trim(),
                page: pageNum,
                limit: limitNum
            });
        } else {
            // 搜索分类
            const categories = await CategoryModel.search(keyword.trim(), language);
            result = {
                categories,
                pagination: {
                    total: categories.length,
                    current_page: 1,
                    total_pages: 1,
                    per_page: categories.length
                },
                keyword: keyword.trim()
            };
        }
        
        return ResponseUtil.success(res, result, '搜索完成');
    });
    
    // ==================== 站点信息 ====================
    
    /**
     * 获取站点配置
     * GET /public/site-config?language=en
     */
    static getSiteConfig = asyncHandler(async (req, res) => {
        // 检查参数验证结果
        if (checkValidationErrors(req, res)) {
            return;
        }
        
        const { language } = req.query;
        
        const siteConfig = await ConfigModel.getSiteConfig(language);
        
        if (!siteConfig) {
            // 返回默认配置
            const defaultConfig = {
                site_name: 'Pet Care Blog',
                site_description: 'Your trusted source for pet care information',
                google_analytics_code: '',
                google_adsense_code: '',
                ads_enabled: false
            };
            
            return ResponseUtil.success(res, defaultConfig, '获取默认站点配置');
        }
        
        // 只返回公开配置，不包含敏感信息
        const publicConfig = {
            site_name: siteConfig.site_name,
            site_description: siteConfig.site_description,
            google_analytics_code: siteConfig.google_analytics_code || '',
            google_adsense_code: siteConfig.google_adsense_code || '',
            ads_enabled: siteConfig.ads_enabled || false
        };
        
        return ResponseUtil.success(res, publicConfig, '获取站点配置成功');
    });
}

module.exports = PublicController;