/**
 * 媒体控制器
 * 
 * 处理文件上传、媒体管理相关的API请求
 * 提供图片上传、获取媒体列表、删除文件等功能
 * 
 * <AUTHOR> Blog System
 * @version 1.0.0
 */

const { ResponseUtil, asyncHandler } = require('../utils/response');
const uploadService = require('../services/uploadService');

/**
 * 媒体控制器类
 */
class MediaController {
    
    /**
     * 上传图片
     * 处理图片文件上传，支持多种图片格式
     * 自动生成缩略图和不同尺寸版本
     */
    static async uploadImage(req, res) {
        try {
            // 检查是否有文件上传
            if (!req.file) {
                return ResponseUtil.badRequest(res, '请选择要上传的图片文件');
            }
            
            // 获取文件类型参数（article, cover, category, other）
            const type = req.body.type || 'article';
            const altText = req.body.alt_text || '';
            
            // 处理图片上传和压缩
            const result = await uploadService.processImage(req.file, type);
            
            if (result.success) {
                // 添加alt文本到返回数据
                result.data.alt_text = altText;
                
                return ResponseUtil.created(res, result.data, '图片上传成功');
            } else {
                return ResponseUtil.serverError(res, '图片处理失败');
            }
        } catch (error) {
            console.error('图片上传失败:', error);
            
            if (error.message.includes('不支持的文件类型')) {
                return ResponseUtil.badRequest(res, error.message);
            }
            
            return ResponseUtil.serverError(res, '图片上传失败，请稍后重试', error);
        }
    }
    
    /**
     * 批量上传图片
     * 支持同时上传多个图片文件
     */
    static async uploadMultipleImages(req, res) {
        try {
            // 检查是否有文件上传
            if (!req.files || req.files.length === 0) {
                return ResponseUtil.badRequest(res, '请选择要上传的图片文件');
            }
            
            const type = req.body.type || 'article';
            const results = [];
            const errors = [];
            
            // 处理每个上传的文件
            for (const file of req.files) {
                try {
                    const result = await uploadService.processImage(file, type);
                    if (result.success) {
                        results.push(result.data);
                    } else {
                        errors.push({
                            file: file.originalname,
                            error: '处理失败'
                        });
                    }
                } catch (error) {
                    errors.push({
                        file: file.originalname,
                        error: error.message
                    });
                }
            }
            
            // 返回处理结果
            const data = {
                uploaded: results,
                failed: errors,
                total: req.files.length,
                success_count: results.length,
                failed_count: errors.length
            };
            
            if (results.length > 0) {
                return ResponseUtil.success(res, data, `成功上传 ${results.length} 个文件`);
            } else {
                return ResponseUtil.serverError(res, '所有文件上传失败', data);
            }
        } catch (error) {
            console.error('批量上传失败:', error);
            return ResponseUtil.serverError(res, '批量上传失败，请稍后重试', error);
        }
    }
    
    /**
     * 上传文档
     * 处理文档文件上传（PDF、Word、Excel等）
     */
    static async uploadDocument(req, res) {
        try {
            // 检查是否有文件上传
            if (!req.file) {
                return ResponseUtil.badRequest(res, '请选择要上传的文档文件');
            }
            
            // 处理文档上传
            const result = await uploadService.processDocument(req.file);
            
            if (result.success) {
                return ResponseUtil.created(res, result.data, '文档上传成功');
            } else {
                return ResponseUtil.serverError(res, '文档处理失败');
            }
        } catch (error) {
            console.error('文档上传失败:', error);
            
            if (error.message.includes('不支持的文件类型')) {
                return ResponseUtil.badRequest(res, error.message);
            }
            
            return ResponseUtil.serverError(res, '文档上传失败，请稍后重试', error);
        }
    }
    
    /**
     * 获取媒体列表
     * 分页获取已上传的媒体文件列表
     */
    static async getMediaList(req, res) {
        try {
            // 获取查询参数
            const type = req.query.type || 'all'; // all, image, document
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 20;
            
            // 验证参数
            if (page < 1) {
                return ResponseUtil.badRequest(res, '页码必须大于0');
            }
            
            if (limit < 1 || limit > 100) {
                return ResponseUtil.badRequest(res, '每页数量必须在1-100之间');
            }
            
            if (!['all', 'image', 'document'].includes(type)) {
                return ResponseUtil.badRequest(res, '无效的文件类型');
            }
            
            // 获取文件列表
            const result = await uploadService.getFileList(type, page, limit);
            
            if (result.success) {
                return ResponseUtil.paginated(
                    res,
                    result.data.files,
                    result.data.pagination,
                    '获取媒体列表成功'
                );
            } else {
                return ResponseUtil.serverError(res, '获取媒体列表失败');
            }
        } catch (error) {
            console.error('获取媒体列表失败:', error);
            return ResponseUtil.serverError(res, '获取媒体列表失败，请稍后重试', error);
        }
    }
    
    /**
     * 删除媒体文件
     * 删除指定的媒体文件
     */
    static async deleteMedia(req, res) {
        try {
            const { path } = req.body;
            
            if (!path) {
                return ResponseUtil.badRequest(res, '请提供要删除的文件路径');
            }
            
            // 安全检查：防止路径遍历攻击
            if (path.includes('..') || path.includes('//')) {
                return ResponseUtil.forbidden(res, '非法的文件路径');
            }
            
            // 确保只能删除uploads目录下的文件
            if (!path.startsWith('uploads/')) {
                return ResponseUtil.forbidden(res, '只能删除上传的文件');
            }
            
            // 删除文件
            const result = await uploadService.deleteFile(path);
            
            if (result.success) {
                return ResponseUtil.success(res, null, result.message);
            } else {
                return ResponseUtil.notFound(res, result.message);
            }
        } catch (error) {
            console.error('删除文件失败:', error);
            return ResponseUtil.serverError(res, '删除文件失败，请稍后重试', error);
        }
    }
    
    /**
     * 批量删除媒体文件
     * 删除多个媒体文件
     */
    static async deleteMultipleMedia(req, res) {
        try {
            const { paths } = req.body;
            
            if (!paths || !Array.isArray(paths) || paths.length === 0) {
                return ResponseUtil.badRequest(res, '请提供要删除的文件路径列表');
            }
            
            const results = [];
            const errors = [];
            
            // 处理每个文件删除
            for (const path of paths) {
                // 安全检查
                if (path.includes('..') || path.includes('//') || !path.startsWith('uploads/')) {
                    errors.push({
                        path: path,
                        error: '非法的文件路径'
                    });
                    continue;
                }
                
                try {
                    const result = await uploadService.deleteFile(path);
                    if (result.success) {
                        results.push(path);
                    } else {
                        errors.push({
                            path: path,
                            error: result.message
                        });
                    }
                } catch (error) {
                    errors.push({
                        path: path,
                        error: error.message
                    });
                }
            }
            
            // 返回处理结果
            const data = {
                deleted: results,
                failed: errors,
                total: paths.length,
                success_count: results.length,
                failed_count: errors.length
            };
            
            if (results.length > 0) {
                return ResponseUtil.success(res, data, `成功删除 ${results.length} 个文件`);
            } else {
                return ResponseUtil.serverError(res, '所有文件删除失败', data);
            }
        } catch (error) {
            console.error('批量删除失败:', error);
            return ResponseUtil.serverError(res, '批量删除失败，请稍后重试', error);
        }
    }
    
    /**
     * 获取存储统计信息
     * 获取媒体存储空间使用情况统计
     */
    static async getStorageStats(req, res) {
        try {
            const result = await uploadService.getStorageStats();
            
            if (result.success) {
                // 格式化存储大小
                const formatSize = (bytes) => {
                    if (bytes === 0) return '0 Bytes';
                    const k = 1024;
                    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                };
                
                const formattedData = {
                    images: {
                        count: result.data.images.count,
                        size: formatSize(result.data.images.size),
                        size_bytes: result.data.images.size
                    },
                    documents: {
                        count: result.data.documents.count,
                        size: formatSize(result.data.documents.size),
                        size_bytes: result.data.documents.size
                    },
                    total: {
                        count: result.data.total.count,
                        size: formatSize(result.data.total.size),
                        size_bytes: result.data.total.size
                    }
                };
                
                return ResponseUtil.success(res, formattedData, '获取存储统计成功');
            } else {
                return ResponseUtil.serverError(res, '获取存储统计失败');
            }
        } catch (error) {
            console.error('获取存储统计失败:', error);
            return ResponseUtil.serverError(res, '获取存储统计失败，请稍后重试', error);
        }
    }
    
    /**
     * 清理临时文件
     * 手动触发清理超过24小时的临时文件
     */
    static async cleanupTemp(req, res) {
        try {
            await uploadService.cleanupTempFiles();
            return ResponseUtil.success(res, null, '临时文件清理完成');
        } catch (error) {
            console.error('清理临时文件失败:', error);
            return ResponseUtil.serverError(res, '清理临时文件失败，请稍后重试', error);
        }
    }
}

module.exports = MediaController;