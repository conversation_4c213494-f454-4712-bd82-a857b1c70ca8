/**
 * 文章数据模型
 * 
 * 提供文章的完整数据操作功能，包括：
 * - 文章基础CRUD操作
 * - 多语言翻译管理
 * - 分类关联管理
 * - 搜索和分页查询
 * - 浏览量统计
 * 
 * <AUTHOR> Blog System
 * @version 1.0.0
 */

const { query, transaction, transactionQuery } = require('../config/database');

/**
 * 文章模型类
 */
class ArticleModel {
    
    /**
     * 创建新文章
     * @param {Object} articleData - 文章数据
     * @param {string} articleData.title_zh - 中文标题
     * @param {string} articleData.content_zh - 中文内容
     * @param {string} articleData.excerpt_zh - 中文摘要
     * @param {string} articleData.cover_image - 封面图片
     * @param {Array} categoryIds - 分类ID数组
     * @returns {Promise<Object>} 创建的文章信息
     */
    static async create(articleData, categoryIds = []) {
        return await transaction(async (connection) => {
            try {
                // 插入文章主记录
                const insertArticleSQL = `
                    INSERT INTO articles 
                    (title_zh, content_zh, excerpt_zh, cover_image, status, view_count) 
                    VALUES (?, ?, ?, ?, 'draft', 0)
                `;
                
                const articleResult = await transactionQuery(connection, insertArticleSQL, [
                    articleData.title_zh,
                    articleData.content_zh,
                    articleData.excerpt_zh || null,
                    articleData.cover_image || null
                ]);
                
                const articleId = articleResult.insertId;
                
                // 插入分类关联
                if (categoryIds.length > 0) {
                    const categoryInsertSQL = `
                        INSERT INTO article_categories (article_id, category_id) 
                        VALUES (?, ?)
                    `;
                    
                    for (const categoryId of categoryIds) {
                        await transactionQuery(connection, categoryInsertSQL, [articleId, categoryId]);
                    }
                }
                
                // 构造返回对象
                console.log(`文章创建成功: ID=${articleId}, 标题=${articleData.title_zh}`);
                
                return {
                    id: articleId,
                    title_zh: articleData.title_zh,
                    content_zh: articleData.content_zh,
                    excerpt_zh: articleData.excerpt_zh || null,
                    cover_image: articleData.cover_image || null,
                    status: 'draft',
                    view_count: 0,
                    categories: [],  // 在事务外部获取
                    created_at: new Date()
                };
                
            } catch (error) {
                console.error('创建文章失败:', error);
                throw error;
            }
        });
    }
    
    /**
     * 根据ID获取文章
     * @param {number} id - 文章ID
     * @param {boolean} includeTranslations - 是否包含翻译
     * @param {boolean} includeCategories - 是否包含分类
     * @returns {Promise<Object|null>} 文章信息
     */
    static async findById(id, includeTranslations = false, includeCategories = true) {
        try {
            // 获取文章基础信息
            const articleSQL = `
                SELECT id, title_zh, content_zh, excerpt_zh, cover_image, 
                       status, view_count, created_at, updated_at
                FROM articles 
                WHERE id = ?
            `;
            
            const articles = await query(articleSQL, [id]);
            
            if (articles.length === 0) {
                return null;
            }
            
            const article = articles[0];
            
            // 获取分类信息
            if (includeCategories) {
                article.categories = await ArticleModel.getArticleCategories(id);
            }
            
            // 获取翻译信息
            if (includeTranslations) {
                article.translations = await ArticleModel.getArticleTranslations(id);
            }
            
            return article;
            
        } catch (error) {
            console.error('获取文章失败:', error);
            throw error;
        }
    }
    
    /**
     * 更新文章
     * @param {number} id - 文章ID
     * @param {Object} updateData - 更新数据
     * @param {Array} categoryIds - 分类ID数组（可选）
     * @returns {Promise<Object>} 更新后的文章
     */
    static async update(id, updateData, categoryIds = null) {
        return await transaction(async (connection) => {
            try {
                // 构建更新SQL
                const updateFields = [];
                const updateValues = [];
                
                const allowedFields = ['title_zh', 'content_zh', 'excerpt_zh', 'cover_image', 'status'];
                
                for (const field of allowedFields) {
                    if (updateData[field] !== undefined) {
                        updateFields.push(`${field} = ?`);
                        updateValues.push(updateData[field]);
                    }
                }
                
                if (updateFields.length === 0) {
                    throw new Error('没有可更新的字段');
                }
                
                updateValues.push(id);
                
                const updateSQL = `
                    UPDATE articles 
                    SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP 
                    WHERE id = ?
                `;
                
                await transactionQuery(connection, updateSQL, updateValues);
                
                // 更新分类关联
                if (categoryIds !== null) {
                    // 删除现有关联
                    await transactionQuery(connection, 
                        'DELETE FROM article_categories WHERE article_id = ?', [id]);
                    
                    // 插入新关联
                    if (categoryIds.length > 0) {
                        const categoryInsertSQL = `
                            INSERT INTO article_categories (article_id, category_id) 
                            VALUES (?, ?)
                        `;
                        
                        for (const categoryId of categoryIds) {
                            await transactionQuery(connection, categoryInsertSQL, [id, categoryId]);
                        }
                    }
                }
                
                console.log(`文章更新成功: ID=${id}`);
                return await ArticleModel.findById(id);
                
            } catch (error) {
                console.error('更新文章失败:', error);
                throw error;
            }
        });
    }
    
    /**
     * 删除文章
     * @param {number} id - 文章ID
     * @returns {Promise<boolean>} 是否删除成功
     */
    static async delete(id) {
        try {
            const deleteSQL = 'DELETE FROM articles WHERE id = ?';
            const result = await query(deleteSQL, [id]);
            
            const success = result.affectedRows > 0;
            
            if (success) {
                console.log(`文章删除成功: ID=${id}`);
            } else {
                console.log(`文章不存在: ID=${id}`);
            }
            
            return success;
            
        } catch (error) {
            console.error('删除文章失败:', error);
            throw error;
        }
    }
    
    /**
     * 获取文章列表（管理后台用）
     * @param {Object} options - 查询选项
     * @param {number} options.page - 页码
     * @param {number} options.limit - 每页数量
     * @param {string} options.status - 状态筛选
     * @param {string} options.search - 搜索关键词
     * @returns {Promise<Object>} 分页结果
     */
    static async getAdminList(options = {}) {
        try {
            const {
                page = 1,
                limit = 10,
                status = null,
                search = null
            } = options;
            
            const offset = Math.max(0, (page - 1) * limit);
            
            // 构建查询条件
            const conditions = [];
            const params = [];
            
            if (status) {
                conditions.push('a.status = ?');
                params.push(status);
            }
            
            if (search) {
                conditions.push('(a.title_zh LIKE ? OR a.content_zh LIKE ?)');
                params.push(`%${search}%`, `%${search}%`);
            }
            
            const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
            
            // 获取总数
            const countSQL = `
                SELECT COUNT(*) as total 
                FROM articles a 
                ${whereClause}
            `;
            const countResult = await query(countSQL, params);
            const total = countResult[0].total;
            
            // 获取文章列表 - 使用字符串拼接LIMIT以避免MySQL参数化问题
            const articlesSQL = `
                SELECT a.id, a.title_zh, a.excerpt_zh, a.cover_image, a.status, 
                       a.view_count, a.created_at, a.updated_at
                FROM articles a 
                ${whereClause}
                ORDER BY a.created_at DESC 
                LIMIT ${offset}, ${limit}
            `;
            
            const articles = await query(articlesSQL, params);
            
            // 为每个文章获取分类信息
            for (const article of articles) {
                article.categories = await ArticleModel.getArticleCategories(article.id);
                article.translation_count = await ArticleModel.getTranslationCount(article.id);
            }
            
            return {
                articles,
                pagination: {
                    total,
                    current_page: page,
                    total_pages: Math.ceil(total / limit),
                    per_page: limit
                }
            };
            
        } catch (error) {
            console.error('获取文章列表失败:', error);
            throw error;
        }
    }
    
    /**
     * 获取公开文章列表（前端用）
     * @param {Object} options - 查询选项
     * @param {string} options.language - 语言代码
     * @param {number} options.categoryId - 分类ID
     * @param {number} options.page - 页码
     * @param {number} options.limit - 每页数量
     * @param {string} options.search - 搜索关键词
     * @returns {Promise<Object>} 分页结果
     */
    static async getPublicList(options = {}) {
        try {
            const {
                language,
                categoryId = null,
                page = 1,
                limit = 10,
                search = null
            } = options;
            
            if (!language) {
                throw new Error('语言代码必需');
            }
            
            // 确保page和limit是整数
            const pageInt = parseInt(page) || 1;
            const limitInt = parseInt(limit) || 10;
            const offset = Math.max(0, (pageInt - 1) * limitInt);
            
            // 构建查询条件
            const conditions = ['t.status = "published"', 't.language = ?'];
            const params = [language];
            
            if (categoryId) {
                conditions.push('EXISTS (SELECT 1 FROM article_categories ac WHERE ac.article_id = a.id AND ac.category_id = ?)');
                params.push(categoryId);
            }
            
            if (search) {
                conditions.push('(t.title LIKE ? OR t.content LIKE ?)');
                params.push(`%${search}%`, `%${search}%`);
            }
            
            const whereClause = `WHERE ${conditions.join(' AND ')}`;
            
            // 获取总数
            const countSQL = `
                SELECT COUNT(*) as total 
                FROM articles a 
                INNER JOIN article_translations t ON a.id = t.article_id 
                ${whereClause}
            `;
            const countResult = await query(countSQL, params);
            const total = countResult[0].total;
            
            // 获取文章列表 - 使用字符串拼接LIMIT以避免MySQL参数化问题
            const articlesSQL = `
                SELECT t.id as translation_id, a.id, t.title, t.excerpt, t.slug,
                       a.cover_image, a.view_count, t.published_at
                FROM articles a 
                INNER JOIN article_translations t ON a.id = t.article_id 
                ${whereClause}
                ORDER BY t.published_at DESC 
                LIMIT ${offset}, ${limitInt}
            `;
            
            const articles = await query(articlesSQL, params);
            
            // 为每个文章获取分类信息
            for (const article of articles) {
                article.categories = await ArticleModel.getArticleCategoriesByLanguage(article.id, language);
            }
            
            return {
                articles,
                pagination: {
                    total,
                    current_page: pageInt,
                    total_pages: Math.ceil(total / limitInt),
                    per_page: limitInt
                }
            };
            
        } catch (error) {
            console.error('获取公开文章列表失败:', error);
            throw error;
        }
    }
    
    /**
     * 根据slug获取文章详情（前端用）
     * @param {string} slug - URL别名
     * @param {string} language - 语言代码
     * @param {boolean} incrementView - 是否增加浏览量
     * @returns {Promise<Object|null>} 文章详情
     */
    static async findBySlug(slug, language, incrementView = true) {
        try {
            const articleSQL = `
                SELECT t.id as translation_id, a.id, t.title, t.content, t.excerpt, 
                       t.slug, t.meta_title, t.meta_description, t.meta_keywords,
                       a.cover_image, a.view_count, t.published_at
                FROM articles a 
                INNER JOIN article_translations t ON a.id = t.article_id 
                WHERE t.slug = ? AND t.language = ? AND t.status = 'published'
            `;
            
            const articles = await query(articleSQL, [slug, language]);
            
            if (articles.length === 0) {
                return null;
            }
            
            const article = articles[0];
            
            // 获取分类信息
            article.categories = await ArticleModel.getArticleCategoriesByLanguage(article.id, language);
            
            // 增加浏览量
            if (incrementView) {
                await ArticleModel.incrementViewCount(article.id);
            }
            
            return article;
            
        } catch (error) {
            console.error('根据slug获取文章失败:', error);
            throw error;
        }
    }
    
    /**
     * 增加文章浏览量
     * @param {number} id - 文章ID
     * @returns {Promise<void>}
     */
    static async incrementViewCount(id) {
        try {
            const updateSQL = 'UPDATE articles SET view_count = view_count + 1 WHERE id = ?';
            await query(updateSQL, [id]);
        } catch (error) {
            console.error('增加浏览量失败:', error);
            // 不抛出错误，避免影响文章显示
        }
    }
    
    /**
     * 获取文章的分类信息
     * @param {number} articleId - 文章ID
     * @returns {Promise<Array>} 分类列表
     */
    static async getArticleCategories(articleId) {
        try {
            const categoriesSQL = `
                SELECT c.id, c.parent_id, c.type, c.sort_order
                FROM categories c
                INNER JOIN article_categories ac ON c.id = ac.category_id
                WHERE ac.article_id = ?
                ORDER BY c.sort_order
            `;
            
            return await query(categoriesSQL, [articleId]);
            
        } catch (error) {
            console.error('获取文章分类失败:', error);
            throw error;
        }
    }
    
    /**
     * 获取文章的分类信息（包含翻译）
     * @param {number} articleId - 文章ID
     * @param {string} language - 语言代码
     * @returns {Promise<Array>} 分类列表
     */
    static async getArticleCategoriesByLanguage(articleId, language) {
        try {
            const categoriesSQL = `
                SELECT c.id, c.parent_id, c.type, ct.name, ct.slug, ct.description
                FROM categories c
                INNER JOIN article_categories ac ON c.id = ac.category_id
                INNER JOIN category_translations ct ON c.id = ct.category_id
                WHERE ac.article_id = ? AND ct.language = ?
                ORDER BY c.sort_order
            `;
            
            return await query(categoriesSQL, [articleId, language]);
            
        } catch (error) {
            console.error('获取文章分类（含翻译）失败:', error);
            throw error;
        }
    }
    
    /**
     * 获取文章的翻译信息
     * @param {number} articleId - 文章ID
     * @param {string} language - 特定语言（可选）
     * @returns {Promise<Array>} 翻译列表
     */
    static async getArticleTranslations(articleId, language = null) {
        try {
            let translationsSQL = `
                SELECT id, language, title, content, excerpt, slug, 
                       meta_title, meta_description, meta_keywords,
                       status, published_at, created_at, updated_at
                FROM article_translations
                WHERE article_id = ?
            `;
            
            const params = [articleId];
            
            if (language) {
                translationsSQL += ' AND language = ?';
                params.push(language);
            }
            
            translationsSQL += ' ORDER BY language';
            
            return await query(translationsSQL, params);
            
        } catch (error) {
            console.error('获取文章翻译失败:', error);
            throw error;
        }
    }
    
    /**
     * 获取文章翻译数量
     * @param {number} articleId - 文章ID
     * @returns {Promise<number>} 翻译数量
     */
    static async getTranslationCount(articleId) {
        try {
            const countSQL = 'SELECT COUNT(*) as count FROM article_translations WHERE article_id = ?';
            const result = await query(countSQL, [articleId]);
            return result[0].count;
        } catch (error) {
            console.error('获取翻译数量失败:', error);
            return 0;
        }
    }
    
    /**
     * 搜索文章
     * @param {Object} options - 搜索选项
     * @param {string} options.language - 语言代码
     * @param {string} options.keyword - 搜索关键词
     * @param {number} options.page - 页码
     * @param {number} options.limit - 每页数量
     * @returns {Promise<Object>} 搜索结果
     */
    static async search(options = {}) {
        try {
            const {
                language,
                keyword,
                page = 1,
                limit = 10
            } = options;
            
            if (!language || !keyword) {
                throw new Error('语言代码和搜索关键词必需');
            }
            
            const offset = Math.max(0, (page - 1) * limit);
            
            // 搜索条件
            const searchConditions = [
                't.status = "published"',
                't.language = ?',
                '(t.title LIKE ? OR t.content LIKE ? OR t.meta_keywords LIKE ?)'
            ];
            const searchParams = [language, `%${keyword}%`, `%${keyword}%`, `%${keyword}%`];
            
            const whereClause = `WHERE ${searchConditions.join(' AND ')}`;
            
            // 获取搜索结果总数
            const countSQL = `
                SELECT COUNT(*) as total 
                FROM articles a 
                INNER JOIN article_translations t ON a.id = t.article_id 
                ${whereClause}
            `;
            const countResult = await query(countSQL, searchParams);
            const total = countResult[0].total;
            
            // 获取搜索结果 - 使用字符串拼接LIMIT以避免MySQL参数化问题
            const searchSQL = `
                SELECT t.id as translation_id, a.id, t.title, t.excerpt, t.slug,
                       a.cover_image, a.view_count, t.published_at
                FROM articles a 
                INNER JOIN article_translations t ON a.id = t.article_id 
                ${whereClause}
                ORDER BY a.view_count DESC, t.published_at DESC
                LIMIT ${offset}, ${limit}
            `;
            
            const articles = await query(searchSQL, searchParams);
            
            // 为每个文章获取分类信息
            for (const article of articles) {
                article.categories = await ArticleModel.getArticleCategoriesByLanguage(article.id, language);
            }
            
            return {
                articles,
                pagination: {
                    total,
                    current_page: page,
                    total_pages: Math.ceil(total / limit),
                    per_page: limit
                },
                keyword
            };
            
        } catch (error) {
            console.error('搜索文章失败:', error);
            throw error;
        }
    }
}

module.exports = ArticleModel;