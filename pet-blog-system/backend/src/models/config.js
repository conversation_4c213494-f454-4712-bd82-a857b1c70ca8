/**
 * 配置数据模型
 * 
 * 提供站点配置、域名配置和API配置的完整数据操作功能，包括：
 * - 站点配置管理 (site_configs)
 * - 域名配置管理 (domains) 
 * - API配置管理 (api_configs)
 * - 多语言站点配置支持
 * 
 * <AUTHOR> Blog System
 * @version 1.0.0
 */

const { query, transaction, transactionQuery } = require('../config/database');

/**
 * 配置模型类
 */
class ConfigModel {
    
    // ==================== 站点配置操作 ====================
    
    /**
     * 获取站点配置
     * @param {string} language - 语言代码
     * @returns {Promise<Object|null>} 站点配置信息
     */
    static async getSiteConfig(language) {
        try {
            const sql = `
                SELECT * FROM site_configs 
                WHERE language = ?
            `;
            
            const result = await query(sql, [language]);
            
            if (result.length === 0) {
                return null;
            }
            
            return result[0];
            
        } catch (error) {
            console.error('获取站点配置失败:', error);
            throw error;
        }
    }
    
    /**
     * 获取所有站点配置
     * @returns {Promise<Array>} 所有站点配置列表
     */
    static async getAllSiteConfigs() {
        try {
            const sql = `
                SELECT * FROM site_configs 
                ORDER BY language ASC
            `;
            
            const result = await query(sql);
            return result;
            
        } catch (error) {
            console.error('获取所有站点配置失败:', error);
            throw error;
        }
    }
    
    /**
     * 更新站点配置
     * @param {string} language - 语言代码
     * @param {Object} configData - 配置数据
     * @returns {Promise<Object>} 更新后的配置信息
     */
    static async updateSiteConfig(language, configData) {
        try {
            const {
                site_name,
                site_description,
                google_analytics_code,
                google_adsense_code,
                ads_enabled
            } = configData;
            
            // 检查配置是否存在
            const existing = await this.getSiteConfig(language);
            
            if (existing) {
                // 更新现有配置
                const updateSQL = `
                    UPDATE site_configs SET
                        site_name = ?,
                        site_description = ?,
                        google_analytics_code = ?,
                        google_adsense_code = ?,
                        ads_enabled = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE language = ?
                `;
                
                await query(updateSQL, [
                    site_name || existing.site_name,
                    site_description || existing.site_description,
                    google_analytics_code || existing.google_analytics_code,
                    google_adsense_code || existing.google_adsense_code,
                    ads_enabled !== undefined ? ads_enabled : existing.ads_enabled,
                    language
                ]);
                
            } else {
                // 创建新配置
                const insertSQL = `
                    INSERT INTO site_configs 
                    (language, site_name, site_description, google_analytics_code, google_adsense_code, ads_enabled) 
                    VALUES (?, ?, ?, ?, ?, ?)
                `;
                
                await query(insertSQL, [
                    language,
                    site_name || '',
                    site_description || '',
                    google_analytics_code || '',
                    google_adsense_code || '',
                    ads_enabled || false
                ]);
            }
            
            // 返回更新后的配置
            const updatedConfig = await this.getSiteConfig(language);
            console.log(`站点配置更新成功: 语言=${language}`);
            
            return updatedConfig;
            
        } catch (error) {
            console.error('更新站点配置失败:', error);
            throw error;
        }
    }
    
    // ==================== 域名配置操作 ====================
    
    /**
     * 获取所有域名配置
     * @param {string} language - 语言筛选（可选）
     * @returns {Promise<Array>} 域名配置列表
     */
    static async getAllDomains(language = null) {
        try {
            let sql = 'SELECT * FROM domains';
            const params = [];
            
            if (language) {
                sql += ' WHERE language = ?';
                params.push(language);
            }
            
            sql += ' ORDER BY is_active DESC, language ASC';
            
            const result = await query(sql, params);
            return result;
            
        } catch (error) {
            console.error('获取域名配置失败:', error);
            throw error;
        }
    }
    
    /**
     * 根据域名获取配置
     * @param {string} domain - 域名
     * @returns {Promise<Object|null>} 域名配置信息
     */
    static async getDomainByName(domain) {
        try {
            const sql = 'SELECT * FROM domains WHERE domain = ?';
            const result = await query(sql, [domain]);
            
            return result.length > 0 ? result[0] : null;
            
        } catch (error) {
            console.error('获取域名配置失败:', error);
            throw error;
        }
    }
    
    /**
     * 添加域名配置
     * @param {Object} domainData - 域名数据
     * @returns {Promise<Object>} 创建的域名配置
     */
    static async addDomain(domainData) {
        try {
            const { domain, language, is_active = true } = domainData;
            
            // 验证参数
            if (!domain || !language) {
                throw new Error('域名和语言代码不能为空');
            }
            
            // 检查域名是否已存在
            const existing = await this.getDomainByName(domain);
            if (existing) {
                throw new Error(`域名已存在: ${domain}`);
            }
            
            // 插入新域名
            const sql = `
                INSERT INTO domains (domain, language, is_active) 
                VALUES (?, ?, ?)
            `;
            
            const result = await query(sql, [domain, language, is_active]);
            
            console.log(`域名配置创建成功: ${domain} -> ${language}`);
            
            return {
                id: result.insertId,
                domain,
                language,
                is_active,
                created_at: new Date()
            };
            
        } catch (error) {
            console.error('添加域名配置失败:', error);
            throw error;
        }
    }
    
    /**
     * 更新域名配置
     * @param {number} id - 域名配置ID
     * @param {Object} updateData - 更新数据
     * @returns {Promise<Object>} 更新后的域名配置
     */
    static async updateDomain(id, updateData) {
        try {
            const { domain, language, is_active } = updateData;
            
            // 检查域名配置是否存在
            const existingSQL = 'SELECT * FROM domains WHERE id = ?';
            const existingResult = await query(existingSQL, [id]);
            
            if (existingResult.length === 0) {
                throw new Error('域名配置不存在');
            }
            
            const existing = existingResult[0];
            
            // 如果更新域名，检查新域名是否已存在
            if (domain && domain !== existing.domain) {
                const duplicateCheck = await this.getDomainByName(domain);
                if (duplicateCheck) {
                    throw new Error(`域名已存在: ${domain}`);
                }
            }
            
            // 构建更新SQL
            const updateSQL = `
                UPDATE domains SET
                    domain = ?,
                    language = ?,
                    is_active = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `;
            
            await query(updateSQL, [
                domain || existing.domain,
                language || existing.language,
                is_active !== undefined ? is_active : existing.is_active,
                id
            ]);
            
            // 返回更新后的数据
            const updatedResult = await query(existingSQL, [id]);
            
            console.log(`域名配置更新成功: ID=${id}`);
            return updatedResult[0];
            
        } catch (error) {
            console.error('更新域名配置失败:', error);
            throw error;
        }
    }
    
    /**
     * 删除域名配置
     * @param {number} id - 域名配置ID
     * @returns {Promise<boolean>} 删除是否成功
     */
    static async deleteDomain(id) {
        try {
            // 检查域名配置是否存在
            const existingSQL = 'SELECT * FROM domains WHERE id = ?';
            const existingResult = await query(existingSQL, [id]);
            
            if (existingResult.length === 0) {
                throw new Error('域名配置不存在');
            }
            
            // 删除域名配置
            const deleteSQL = 'DELETE FROM domains WHERE id = ?';
            const result = await query(deleteSQL, [id]);
            
            console.log(`域名配置删除成功: ID=${id}`);
            return result.affectedRows > 0;
            
        } catch (error) {
            console.error('删除域名配置失败:', error);
            throw error;
        }
    }
    
    // ==================== API配置操作 ====================
    
    /**
     * 获取所有API配置
     * @param {boolean} activeOnly - 是否只获取激活的配置
     * @returns {Promise<Array>} API配置列表
     */
    static async getAllApiConfigs(activeOnly = false) {
        try {
            let sql = 'SELECT * FROM api_configs';
            
            if (activeOnly) {
                sql += ' WHERE is_active = 1';
            }
            
            sql += ' ORDER BY is_active DESC, name ASC';
            
            const result = await query(sql);
            return result;
            
        } catch (error) {
            console.error('获取API配置失败:', error);
            throw error;
        }
    }
    
    /**
     * 根据ID获取API配置
     * @param {number} id - 配置ID
     * @returns {Promise<Object|null>} API配置信息
     */
    static async getApiConfigById(id) {
        try {
            const sql = 'SELECT * FROM api_configs WHERE id = ?';
            const result = await query(sql, [id]);
            
            return result.length > 0 ? result[0] : null;
            
        } catch (error) {
            console.error('获取API配置失败:', error);
            throw error;
        }
    }
    
    /**
     * 根据名称获取API配置
     * @param {string} name - 配置名称
     * @returns {Promise<Object|null>} API配置信息
     */
    static async getApiConfigByName(name) {
        try {
            const sql = 'SELECT * FROM api_configs WHERE name = ?';
            const result = await query(sql, [name]);
            
            return result.length > 0 ? result[0] : null;
            
        } catch (error) {
            console.error('获取API配置失败:', error);
            throw error;
        }
    }
    
    /**
     * 更新API配置
     * @param {Object} configData - 配置数据
     * @returns {Promise<Object>} 更新后的配置信息
     */
    static async updateApiConfig(configData) {
        try {
            const {
                name = 'OpenAI翻译',
                api_url,
                api_key,
                model_name,
                is_active = true
            } = configData;
            
            // 验证必需参数
            if (!api_url || !api_key) {
                throw new Error('API地址和密钥不能为空');
            }
            
            // 检查配置是否存在
            const existing = await this.getApiConfigByName(name);
            
            if (existing) {
                // 更新现有配置
                const updateSQL = `
                    UPDATE api_configs SET
                        api_url = ?,
                        api_key = ?,
                        model_name = ?,
                        is_active = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE name = ?
                `;
                
                await query(updateSQL, [
                    api_url,
                    api_key,
                    model_name || existing.model_name,
                    is_active,
                    name
                ]);
                
                console.log(`API配置更新成功: ${name}`);
                
            } else {
                // 创建新配置
                const insertSQL = `
                    INSERT INTO api_configs 
                    (name, api_url, api_key, model_name, is_active) 
                    VALUES (?, ?, ?, ?, ?)
                `;
                
                await query(insertSQL, [
                    name,
                    api_url,
                    api_key,
                    model_name || '',
                    is_active
                ]);
                
                console.log(`API配置创建成功: ${name}`);
            }
            
            // 返回更新后的配置
            const updatedConfig = await this.getApiConfigByName(name);
            return updatedConfig;
            
        } catch (error) {
            console.error('更新API配置失败:', error);
            throw error;
        }
    }
    
    /**
     * 获取激活的API配置
     * @returns {Promise<Object|null>} 激活的API配置
     */
    static async getActiveApiConfig() {
        try {
            const sql = 'SELECT * FROM api_configs WHERE is_active = 1 LIMIT 1';
            const result = await query(sql);
            
            return result.length > 0 ? result[0] : null;
            
        } catch (error) {
            console.error('获取激活API配置失败:', error);
            throw error;
        }
    }
}

module.exports = ConfigModel;