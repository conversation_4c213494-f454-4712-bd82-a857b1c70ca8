/**
 * 评论数据模型
 * 
 * 提供评论系统的完整数据操作功能，包括：
 * - 评论CRUD操作
 * - 多语言支持
 * - 父子评论关系（嵌套评论）
 * - 评论状态管理（待审核/已批准/已拒绝）
 * - 安全功能（IP记录、防XSS等）
 * - 分页查询和统计
 * 
 * <AUTHOR> Blog System
 * @version 1.0.0
 */

const { query, transaction, transactionQuery } = require('../config/database');
const sanitizeHtml = require('sanitize-html');

/**
 * 评论模型类
 */
class CommentModel {
    
    /**
     * 创建新评论
     * @param {Object} commentData - 评论数据
     * @param {number} commentData.article_id - 文章ID
     * @param {string} commentData.language - 语言代码(en/de/ru)
     * @param {number} commentData.parent_id - 父评论ID（可选）
     * @param {string} commentData.username - 用户名
     * @param {string} commentData.email - 邮箱
     * @param {string} commentData.content - 评论内容
     * @param {string} commentData.ip_address - IP地址
     * @param {string} commentData.user_agent - 用户代理
     * @returns {Promise<Object>} 创建的评论信息
     */
    static async create(commentData) {
        try {
            // 清理和验证输入数据
            const cleanData = {
                article_id: parseInt(commentData.article_id),
                language: commentData.language,
                parent_id: commentData.parent_id ? parseInt(commentData.parent_id) : null,
                username: this.sanitizeInput(commentData.username),
                email: commentData.email.toLowerCase().trim(),
                content: this.sanitizeContent(commentData.content),
                ip_address: commentData.ip_address || null,
                user_agent: commentData.user_agent || null,
                status: 'pending' // 默认待审核
            };
            
            // 验证文章是否存在
            const articleExists = await query(
                'SELECT id FROM articles WHERE id = ?',
                [cleanData.article_id]
            );
            
            if (articleExists.length === 0) {
                throw new Error('文章不存在');
            }
            
            // 如果有父评论，验证父评论是否存在且属于同一文章
            if (cleanData.parent_id) {
                const parentComment = await query(
                    'SELECT id FROM comments WHERE id = ? AND article_id = ? AND status = "approved"',
                    [cleanData.parent_id, cleanData.article_id]
                );
                
                if (parentComment.length === 0) {
                    throw new Error('父评论不存在或未审核');
                }
            }
            
            // 插入评论
            const insertSQL = `
                INSERT INTO comments 
                (article_id, language, parent_id, username, email, content, status, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;
            
            const result = await query(insertSQL, [
                cleanData.article_id,
                cleanData.language,
                cleanData.parent_id,
                cleanData.username,
                cleanData.email,
                cleanData.content,
                cleanData.status,
                cleanData.ip_address,
                cleanData.user_agent
            ]);
            
            console.log(`评论创建成功: ID=${result.insertId}, 文章ID=${cleanData.article_id}, 用户=${cleanData.username}`);
            
            return {
                id: result.insertId,
                ...cleanData,
                created_at: new Date()
            };
            
        } catch (error) {
            console.error('创建评论失败:', error);
            throw error;
        }
    }
    
    /**
     * 根据ID获取评论
     * @param {number} id - 评论ID
     * @param {boolean} includeReplies - 是否包含回复
     * @returns {Promise<Object|null>} 评论信息
     */
    static async findById(id, includeReplies = false) {
        try {
            const commentSQL = `
                SELECT id, article_id, language, parent_id, username, email, 
                       content, status, ip_address, user_agent, created_at, updated_at
                FROM comments 
                WHERE id = ?
            `;
            
            const comments = await query(commentSQL, [id]);
            
            if (comments.length === 0) {
                return null;
            }
            
            const comment = comments[0];
            
            // 如果需要包含回复
            if (includeReplies && comment.status === 'approved') {
                const repliesSQL = `
                    SELECT id, article_id, language, parent_id, username, email, 
                           content, status, created_at
                    FROM comments 
                    WHERE parent_id = ? AND status = 'approved'
                    ORDER BY created_at ASC
                `;
                
                comment.replies = await query(repliesSQL, [id]);
            }
            
            return comment;
            
        } catch (error) {
            console.error('获取评论失败:', error);
            throw error;
        }
    }
    
    /**
     * 获取文章的评论列表（分页）
     * @param {number} articleId - 文章ID
     * @param {string} language - 语言代码
     * @param {Object} options - 查询选项
     * @param {number} options.page - 页码（默认1）
     * @param {number} options.limit - 每页数量（默认20）
     * @param {string} options.status - 状态筛选（默认approved）
     * @param {boolean} options.includeReplies - 是否包含回复（默认true）
     * @returns {Promise<Object>} 评论列表和分页信息
     */
    static async findByArticle(articleId, language, options = {}) {
        try {
            const page = parseInt(options.page) || 1;
            const limit = parseInt(options.limit) || 20;
            const offset = (page - 1) * limit;
            const status = options.status || 'approved';
            const includeReplies = options.includeReplies !== false;
            
            // 获取顶级评论（parent_id为NULL）
            const commentsSQL = `
                SELECT id, article_id, language, parent_id, username, email, 
                       content, status, created_at
                FROM comments 
                WHERE article_id = ? AND language = ? AND parent_id IS NULL AND status = ?
                ORDER BY created_at DESC
                LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
            `;
            
            const comments = await query(commentsSQL, [
                articleId, 
                language, 
                status
            ]);
            
            // 如果需要包含回复，获取所有顶级评论的回复
            if (includeReplies && comments.length > 0) {
                const topLevelIds = comments.map(c => c.id);
                
                const repliesSQL = `
                    SELECT id, article_id, language, parent_id, username, email, 
                           content, status, created_at
                    FROM comments 
                    WHERE parent_id IN (${topLevelIds.map(() => '?').join(',')}) 
                    AND status = 'approved'
                    ORDER BY created_at ASC
                `;
                
                const replies = await query(repliesSQL, topLevelIds);
                
                // 将回复分配给对应的顶级评论
                const repliesMap = {};
                replies.forEach(reply => {
                    if (!repliesMap[reply.parent_id]) {
                        repliesMap[reply.parent_id] = [];
                    }
                    repliesMap[reply.parent_id].push(reply);
                });
                
                comments.forEach(comment => {
                    comment.replies = repliesMap[comment.id] || [];
                });
            }
            
            // 获取总数用于分页
            const countSQL = `
                SELECT COUNT(*) as total 
                FROM comments 
                WHERE article_id = ? AND language = ? AND parent_id IS NULL AND status = ?
            `;
            
            const countResult = await query(countSQL, [articleId, language, status]);
            const total = countResult[0].total;
            
            return {
                comments,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit)
                }
            };
            
        } catch (error) {
            console.error('获取文章评论失败:', error);
            throw error;
        }
    }
    
    /**
     * 更新评论状态
     * @param {number} id - 评论ID
     * @param {string} status - 新状态(pending/approved/rejected)
     * @returns {Promise<boolean>} 更新是否成功
     */
    static async updateStatus(id, status) {
        try {
            const validStatuses = ['pending', 'approved', 'rejected'];
            if (!validStatuses.includes(status)) {
                throw new Error('无效的状态值');
            }
            
            const updateSQL = `
                UPDATE comments 
                SET status = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            `;
            
            const result = await query(updateSQL, [status, id]);
            
            if (result.affectedRows > 0) {
                console.log(`评论状态更新成功: ID=${id}, 新状态=${status}`);
                return true;
            }
            
            return false;
            
        } catch (error) {
            console.error('更新评论状态失败:', error);
            throw error;
        }
    }
    
    /**
     * 批量更新评论状态
     * @param {Array<number>} ids - 评论ID数组
     * @param {string} status - 新状态
     * @returns {Promise<number>} 更新的数量
     */
    static async updateStatusBatch(ids, status) {
        try {
            if (!ids || ids.length === 0) {
                return 0;
            }
            
            const validStatuses = ['pending', 'approved', 'rejected'];
            if (!validStatuses.includes(status)) {
                throw new Error('无效的状态值');
            }
            
            const placeholders = ids.map(() => '?').join(',');
            const updateSQL = `
                UPDATE comments 
                SET status = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id IN (${placeholders})
            `;
            
            const result = await query(updateSQL, [status, ...ids]);
            
            console.log(`批量更新评论状态成功: 更新${result.affectedRows}条, 新状态=${status}`);
            return result.affectedRows;
            
        } catch (error) {
            console.error('批量更新评论状态失败:', error);
            throw error;
        }
    }
    
    /**
     * 删除评论（软删除，实际是设置为rejected）
     * @param {number} id - 评论ID
     * @returns {Promise<boolean>} 删除是否成功
     */
    static async delete(id) {
        try {
            // 使用软删除策略，将状态设置为rejected
            return await this.updateStatus(id, 'rejected');
            
        } catch (error) {
            console.error('删除评论失败:', error);
            throw error;
        }
    }
    
    /**
     * 硬删除评论（物理删除）
     * @param {number} id - 评论ID
     * @returns {Promise<boolean>} 删除是否成功
     */
    static async hardDelete(id) {
        try {
            // 首先删除所有子评论
            const deleteRepliesSQL = 'DELETE FROM comments WHERE parent_id = ?';
            await query(deleteRepliesSQL, [id]);
            
            // 删除评论本身
            const deleteSQL = 'DELETE FROM comments WHERE id = ?';
            const result = await query(deleteSQL, [id]);
            
            if (result.affectedRows > 0) {
                console.log(`评论硬删除成功: ID=${id}`);
                return true;
            }
            
            return false;
            
        } catch (error) {
            console.error('硬删除评论失败:', error);
            throw error;
        }
    }
    
    /**
     * 获取待审核评论列表
     * @param {Object} options - 查询选项
     * @param {number} options.page - 页码
     * @param {number} options.limit - 每页数量
     * @returns {Promise<Object>} 评论列表和分页信息
     */
    static async findPending(options = {}) {
        try {
            const page = parseInt(options.page) || 1;
            const limit = parseInt(options.limit) || 50;
            const offset = (page - 1) * limit;
            
            const commentsSQL = `
                SELECT c.*, a.title_zh as article_title
                FROM comments c
                LEFT JOIN articles a ON c.article_id = a.id
                WHERE c.status = 'pending'
                ORDER BY c.created_at DESC
                LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
            `;
            
            const comments = await query(commentsSQL, []);
            
            const countSQL = `
                SELECT COUNT(*) as total 
                FROM comments 
                WHERE status = 'pending'
            `;
            
            const countResult = await query(countSQL);
            const total = countResult[0].total;
            
            return {
                comments,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit)
                }
            };
            
        } catch (error) {
            console.error('获取待审核评论失败:', error);
            throw error;
        }
    }

    /**
     * 获取待审核评论数量
     * @returns {Promise<number>} 待审核评论数量
     */
    static async getPendingCount() {
        try {
            const countSQL = `
                SELECT COUNT(*) as count
                FROM comments
                WHERE status = 'pending'
            `;

            const results = await query(countSQL);
            return results[0]?.count || 0;

        } catch (error) {
            console.error('获取待审核评论数量失败:', error);
            throw error;
        }
    }

    /**
     * 获取评论统计信息
     * @param {number} articleId - 文章ID（可选）
     * @returns {Promise<Object>} 统计信息
     */
    static async getStats(articleId = null) {
        try {
            let whereClause = '';
            let params = [];
            
            if (articleId) {
                whereClause = 'WHERE article_id = ?';
                params.push(articleId);
            }
            
            const statsSQL = `
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
                    SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected
                FROM comments
                ${whereClause}
            `;
            
            const result = await query(statsSQL, params);
            
            return {
                total: result[0].total || 0,
                pending: result[0].pending || 0,
                approved: result[0].approved || 0,
                rejected: result[0].rejected || 0
            };
            
        } catch (error) {
            console.error('获取评论统计失败:', error);
            throw error;
        }
    }
    
    /**
     * 搜索评论
     * @param {Object} criteria - 搜索条件
     * @param {string} criteria.keyword - 关键词
     * @param {string} criteria.status - 状态
     * @param {string} criteria.language - 语言
     * @param {string} criteria.startDate - 开始日期
     * @param {string} criteria.endDate - 结束日期
     * @param {Object} options - 分页选项
     * @returns {Promise<Object>} 搜索结果
     */
    static async search(criteria = {}, options = {}) {
        try {
            const page = parseInt(options.page) || 1;
            const limit = parseInt(options.limit) || 20;
            const offset = (page - 1) * limit;
            
            let whereConditions = ['1=1'];
            let params = [];
            
            // 构建搜索条件
            if (criteria.keyword) {
                whereConditions.push('(c.username LIKE ? OR c.email LIKE ? OR c.content LIKE ?)');
                const keywordPattern = `%${criteria.keyword}%`;
                params.push(keywordPattern, keywordPattern, keywordPattern);
            }
            
            if (criteria.status) {
                whereConditions.push('c.status = ?');
                params.push(criteria.status);
            }
            
            if (criteria.language) {
                whereConditions.push('c.language = ?');
                params.push(criteria.language);
            }
            
            if (criteria.startDate) {
                whereConditions.push('c.created_at >= ?');
                params.push(criteria.startDate);
            }
            
            if (criteria.endDate) {
                whereConditions.push('c.created_at <= ?');
                params.push(criteria.endDate);
            }
            
            const whereClause = whereConditions.join(' AND ');
            
            // 查询评论
            const searchSQL = `
                SELECT c.*, a.title_zh as article_title
                FROM comments c
                LEFT JOIN articles a ON c.article_id = a.id
                WHERE ${whereClause}
                ORDER BY c.created_at DESC
                LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
            `;
            
            const comments = await query(searchSQL, params);
            
            // 获取总数
            const countSQL = `
                SELECT COUNT(*) as total 
                FROM comments c
                WHERE ${whereClause}
            `;
            
            const countResult = await query(countSQL, params);
            const total = countResult[0].total;
            
            return {
                comments,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit)
                }
            };
            
        } catch (error) {
            console.error('搜索评论失败:', error);
            throw error;
        }
    }
    
    /**
     * 清理评论内容（防XSS）
     * @param {string} content - 原始内容
     * @returns {string} 清理后的内容
     */
    static sanitizeContent(content) {
        return sanitizeHtml(content, {
            allowedTags: ['b', 'i', 'em', 'strong', 'p', 'br'],
            allowedAttributes: {}
        });
    }
    
    /**
     * 清理输入字符串
     * @param {string} input - 原始输入
     * @returns {string} 清理后的输入
     */
    static sanitizeInput(input) {
        return input.trim().replace(/[<>]/g, '');
    }
    
    /**
     * 验证邮箱格式
     * @param {string} email - 邮箱地址
     * @returns {boolean} 是否有效
     */
    static validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    /**
     * 检查用户是否被封禁（基于IP或邮箱）
     * @param {string} ipAddress - IP地址
     * @param {string} email - 邮箱
     * @returns {Promise<boolean>} 是否被封禁
     */
    static async isUserBanned(ipAddress, email) {
        try {
            // 检查最近被拒绝的评论数量
            const recentRejectedSQL = `
                SELECT COUNT(*) as count
                FROM comments
                WHERE (ip_address = ? OR email = ?)
                AND status = 'rejected'
                AND created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
            `;
            
            const result = await query(recentRejectedSQL, [ipAddress, email]);
            
            // 如果30天内有超过5条被拒绝的评论，认为用户被封禁
            return result[0].count > 5;
            
        } catch (error) {
            console.error('检查用户封禁状态失败:', error);
            return false;
        }
    }
}

module.exports = CommentModel;