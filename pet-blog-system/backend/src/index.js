/**
 * 宠物博客站群系统 - Express应用主文件
 * 
 * 这是后端API服务的核心入口文件，提供：
 * - RESTful API服务
 * - 多语言内容管理
 * - AI翻译集成
 * - 安全防护和性能优化
 * 
 * <AUTHOR> Blog System
 * @version 1.0.0
 */

const express = require('express');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const path = require('path');

// 加载环境变量
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// 导入数据库连接
const db = require('./config/database');

// 导入中间件
const { corsMiddleware, validateCorsConfig } = require('./middleware/cors');
const { notFoundHandler, globalErrorHandler } = require('./middleware/errorHandler');
const {
    publicAPILimiter,
    adminAPILimiter,
    uploadAPILimiter,
    authAPILimiter,
    validateRateLimitConfig
} = require('./middleware/rateLimit');

// 创建Express应用实例
const app = express();

// ====================================
// 基础配置
// ====================================

// 设置应用信息
app.set('trust proxy', 1); // 信任第一个代理
app.set('x-powered-by', false); // 隐藏Express标识

// 端口配置
const PORT = process.env.PORT || 3000;
const NODE_ENV = process.env.NODE_ENV || 'development';

console.log(`启动环境: ${NODE_ENV}`);
console.log(`服务端口: ${PORT}`);

// ====================================
// 安全中间件
// ====================================

// Helmet - 设置安全HTTP头
app.use(helmet({
    contentSecurityPolicy: NODE_ENV === 'production' ? {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"]
        }
    } : false,
    crossOriginEmbedderPolicy: false
}));

// CORS - 跨域配置（使用专门的CORS中间件）
app.use(corsMiddleware);

// ====================================
// 基础中间件
// ====================================

// HTTP请求日志
if (NODE_ENV === 'development') {
    app.use(morgan('dev'));
} else {
    app.use(morgan('combined'));
}

// 响应压缩
app.use(compression({
    level: 6,
    threshold: 1024,
    filter: (req, res) => {
        if (req.headers['x-no-compression']) {
            return false;
        }
        return compression.filter(req, res);
    }
}));

// 请求体解析
app.use(express.json({
    limit: '10mb',
    type: ['application/json', 'text/plain']
}));

app.use(express.urlencoded({
    extended: true,
    limit: '10mb'
}));

// 静态文件服务 (上传的文件)
app.use('/uploads', express.static(path.join(__dirname, '../uploads'), {
    maxAge: NODE_ENV === 'production' ? '7d' : '1h',
    etag: true,
    lastModified: true
}));

// ====================================
// API限流中间件配置
// ====================================

// 应用不同类型API的专业限流策略
app.use('/api/public', publicAPILimiter);           // 公开API: 100次/分钟
app.use('/api/admin/auth', authAPILimiter);         // 认证API: 5次/15分钟  
app.use('/api/admin/media/upload', uploadAPILimiter); // 上传API: 10次/分钟
app.use('/api/admin', adminAPILimiter);             // 管理API: 1000次/分钟

// ====================================
// 请求日志和性能监控中间件
// ====================================

app.use((req, res, next) => {
    req.startTime = Date.now();
    
    if (process.env.ENABLE_REQUEST_LOGGING === 'true') {
        console.log(`${new Date().toISOString()} - ${req.method} ${req.path} - ${req.ip}`);
    }
    
    res.on('finish', () => {
        const duration = Date.now() - req.startTime;
        
        if (process.env.ENABLE_PERFORMANCE_MONITORING === 'true' && 
            duration > parseInt(process.env.API_RESPONSE_TIME_WARNING) || 500) {
            console.warn(`慢请求警告: ${req.method} ${req.path} - ${duration}ms`);
        }
    });
    
    next();
});

// ====================================
// 健康检查端点
// ====================================

app.get('/health', async (req, res) => {
    const { ResponseUtil } = require('./utils/response');
    
    try {
        const dbHealth = await db.healthCheck();
        
        const healthStatus = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            environment: NODE_ENV,
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            database: dbHealth
        };
        
        ResponseUtil.success(res, healthStatus, '健康检查成功');
    } catch (err) {
        console.error('健康检查失败:', err);
        ResponseUtil.error(res, '健康检查失败', 503, { 
            status: 'unhealthy',
            error: err.message 
        });
    }
});

// API基础信息端点
app.get('/api', (req, res) => {
    res.json({
        success: true,
        code: 200,
        message: '宠物博客站群系统 API',
        data: {
            version: '1.0.0',
            name: '宠物博客站群系统后端API',
            description: '支持多语言内容管理和AI翻译的宠物博客系统',
            environment: NODE_ENV,
            features: [
                '多语言内容管理',
                'AI自动翻译',
                '站群SEO优化',
                '评论系统',
                '媒体管理',
                'RESTful API'
            ],
            endpoints: {
                public: '/api/public/*',
                admin: '/api/admin/*',
                health: '/health'
            }
        },
        timestamp: Date.now()
    });
});

// ====================================
// 路由挂载点 - 步骤39完成：所有路由已整合
// ====================================

// 路由顺序很重要：具体路由在前，通用路由在后

// 1. 公开API路由（不需要认证）
app.use('/api/public', require('./routes/public'));      // 公开API路由（文章、分类、评论等公开接口）

// 2. 认证路由（登录、注册等）
app.use('/api/admin/auth', require('./routes/auth'));    // 认证路由（登录、注册、刷新token）

// 3. 管理后台API路由（需要认证）
app.use('/api/admin/media', require('./routes/media'));  // 媒体管理路由（上传、删除等）
app.use('/api', require('./routes/translation'));        // 翻译管理路由（AI翻译、更新、发布等）
app.use('/api', require('./routes/config'));            // 配置管理路由（站点配置、多语言配置等）

// 4. 通用资源路由（包含admin和public路由）
app.use('/api', require('./routes/article'));            // 文章路由（增删改查、发布等）
app.use('/api', require('./routes/category'));           // 分类路由（增删改查、层级管理等）
app.use('/api', require('./routes/comment'));            // 评论路由（审核、回复、删除等）

console.log('='.repeat(50));
console.log('✅ 步骤39完成：所有路由已成功整合');
console.log('📋 已注册路由：');
console.log('  - 公开API路由 (/api/public/*)');
console.log('  - 认证路由 (/api/admin/auth/*)');
console.log('  - 媒体管理路由 (/api/admin/media/*)');
console.log('  - 翻译管理路由 (/api/admin/articles/*/translate)');
console.log('  - 配置管理路由 (/api/admin/configs/*)');
console.log('  - 文章管理路由 (/api/admin/articles/*)');
console.log('  - 分类管理路由 (/api/admin/categories/*)');
console.log('  - 评论管理路由 (/api/admin/comments/*)');
console.log('='.repeat(50));

// ====================================
// 错误处理中间件
// ====================================

// 404处理 - 统一错误处理中间件
app.use('*', notFoundHandler);

// 全局错误处理中间件 - 统一错误处理中间件
app.use(globalErrorHandler);

// ====================================
// 优雅关闭处理
// ====================================

// 验证配置
validateCorsConfig();
validateRateLimitConfig();

const server = app.listen(PORT, () => {
    console.log('='.repeat(50));
    console.log('🚀 宠物博客站群系统 - 后端服务启动成功');
    console.log('='.repeat(50));
    console.log(`🌐 服务地址: http://localhost:${PORT}`);
    console.log(`📊 健康检查: http://localhost:${PORT}/health`);
    console.log(`📖 API文档: http://localhost:${PORT}/api`);
    console.log(`🔧 运行环境: ${NODE_ENV}`);
    console.log(`📁 数据库: ${db.config.host}:${db.config.port}/${db.config.database}`);
    console.log('='.repeat(50));
});

// 优雅关闭处理
const gracefulShutdown = async (signal) => {
    console.log(`\n接收到 ${signal} 信号，开始优雅关闭...`);
    
    server.close(async (err) => {
        if (err) {
            console.error('关闭HTTP服务器时出错:', err);
            process.exit(1);
        }
        
        console.log('HTTP服务器已关闭');
        
        try {
            await db.closePool();
            console.log('数据库连接池已关闭');
            console.log('应用程序已优雅关闭');
            process.exit(0);
        } catch (error) {
            console.error('关闭数据库连接时出错:', error);
            process.exit(1);
        }
    });
    
    // 强制关闭超时
    setTimeout(() => {
        console.error('强制关闭应用程序（超时）');
        process.exit(1);
    }, 10000);
};

// 监听系统信号
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// 监听未捕获的异常
process.on('uncaughtException', (error) => {
    console.error('未捕获的异常:', error);
    gracefulShutdown('UNCAUGHT_EXCEPTION');
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('未处理的Promise拒绝:', reason);
    console.error('Promise:', promise);
    gracefulShutdown('UNHANDLED_REJECTION');
});

// 导出应用实例 (用于测试)
module.exports = app;