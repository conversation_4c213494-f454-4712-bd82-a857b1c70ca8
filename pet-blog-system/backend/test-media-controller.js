const MediaController = require('./src/controllers/mediaController');
const { asyncHandler } = require('./src/utils/response');

console.log('MediaController:', MediaController);
console.log('MediaController methods:');
console.log('  - uploadImage:', typeof MediaController.uploadImage);
console.log('  - uploadMultipleImages:', typeof MediaController.uploadMultipleImages);
console.log('  - uploadDocument:', typeof MediaController.uploadDocument);
console.log('  - getMediaList:', typeof MediaController.getMediaList);
console.log('  - deleteMedia:', typeof MediaController.deleteMedia);
console.log('  - deleteMultipleMedia:', typeof MediaController.deleteMultipleMedia);
console.log('  - getStorageStats:', typeof MediaController.getStorageStats);
console.log('  - cleanupTemp:', typeof MediaController.cleanupTemp);

console.log('\nTesting asyncHandler wrapper:');
const wrapped = asyncHandler(MediaController.uploadImage);
console.log('Wrapped uploadImage:', typeof wrapped);

console.log('\nAll MediaController properties:');
console.log(Object.getOwnPropertyNames(MediaController));