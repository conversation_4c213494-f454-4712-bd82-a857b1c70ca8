{"total": 25, "passed": 11, "failed": 14, "errors": [{"test": "获取热门文章", "error": "文章不存在", "status": 404}, {"test": "获取相关文章", "error": "请求的资源不存在", "status": 404}, {"test": "验证Token", "error": "请求的资源不存在", "status": 404}, {"test": "获取管理员信息", "error": "请求的资源不存在", "status": 404}, {"test": "获取文章统计", "error": "无效的文章ID", "status": 400}, {"test": "获取分类树", "error": "无效的分类ID", "status": 400}, {"test": "获取待审核评论数", "error": "请求的资源不存在", "status": 404}, {"test": "获取所有配置", "error": "请求的资源不存在", "status": 404}, {"test": "获取英语配置", "error": "请求的资源不存在", "status": 404}, {"test": "获取翻译队列", "error": "请求的资源不存在", "status": 404}, {"test": "获取上传配置", "error": "请求的资源不存在", "status": 404}, {"test": "404错误处理", "error": "请求的资源不存在", "status": 404}, {"test": "无认证访问管理API", "error": "未提供访问令牌", "status": 401}, {"test": "无效参数错误", "error": "无效的文章ID", "status": 400}], "startTime": "2025-08-09T08:10:46.066Z", "endTime": "2025-08-09T08:11:21.887Z"}