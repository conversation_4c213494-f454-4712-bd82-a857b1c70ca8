{"total": 33, "passed": 6, "failed": 27, "errors": [{"test": "管理员登录", "error": "用户名或密码错误", "status": 401, "details": {"success": false, "code": 401, "message": "用户名或密码错误", "data": null, "timestamp": 1754729045830}}, {"test": "Token验证", "error": "请求的资源不存在", "status": 404, "details": {"success": false, "code": 404, "message": "请求的资源不存在", "data": {"method": "GET", "url": "/api/admin/auth/verify", "availableRoutes": ["/health", "/api"]}, "timestamp": 1754729045833}}, {"test": "获取管理员信息", "error": "请求的资源不存在", "status": 404, "details": {"success": false, "code": 404, "message": "请求的资源不存在", "data": {"method": "GET", "url": "/api/admin/auth/profile", "availableRoutes": ["/health", "/api"]}, "timestamp": 1754729045835}}, {"test": "获取所有分类", "error": "无效的访问令牌", "status": 401, "details": {"success": false, "code": 401, "message": "无效的访问令牌", "data": null, "timestamp": 1754729045837}}, {"test": "创建新分类", "error": "无效的访问令牌", "status": 401, "details": {"success": false, "code": 401, "message": "无效的访问令牌", "data": null, "timestamp": 1754729045839}}, {"test": "获取文章列表", "error": "无效的访问令牌", "status": 401, "details": {"success": false, "code": 401, "message": "无效的访问令牌", "data": null, "timestamp": 1754729046207}}, {"test": "创建新文章", "error": "无效的访问令牌", "status": 401, "details": {"success": false, "code": 401, "message": "无效的访问令牌", "data": null, "timestamp": 1754729046209}}, {"test": "公开API - 获取文章列表", "error": "参数验证失败", "status": 400, "details": {"success": false, "code": 400, "message": "参数验证失败", "data": {"errors": ["语言代码必须是: en, de, ru之一"]}, "timestamp": 1754729046212}}, {"test": "搜索文章", "error": "文章不存在", "status": 404, "details": {"success": false, "code": 404, "message": "文章不存在", "data": null, "timestamp": 1754729046594}}, {"test": "获取评论列表", "error": "无效的访问令牌", "status": 401, "details": {"success": false, "code": 401, "message": "无效的访问令牌", "data": null, "timestamp": 1754729046596}}, {"test": "提交评论", "error": "缺少必需参数：article_id、language、username、email、content", "status": 400, "details": {"success": false, "code": 400, "message": "缺少必需参数：article_id、language、username、email、content", "data": null, "timestamp": 1754729046599}}, {"test": "获取文章评论", "error": "请求的资源不存在", "status": 404, "details": {"success": false, "code": 404, "message": "请求的资源不存在", "data": {"method": "GET", "url": "/api/public/articles/1/comments?language=zh", "availableRoutes": ["/health", "/api"]}, "timestamp": 1754729046601}}, {"test": "获取待审核评论数", "error": "请求的资源不存在", "status": 404, "details": {"success": false, "code": 404, "message": "请求的资源不存在", "data": {"method": "GET", "url": "/api/admin/comments/pending-count", "availableRoutes": ["/health", "/api"]}, "timestamp": 1754729046603}}, {"test": "获取翻译配置", "error": "无效的访问令牌", "status": 401, "details": {"success": false, "code": 401, "message": "无效的访问令牌", "data": null, "timestamp": 1754729046604}}, {"test": "翻译文章（模拟）", "error": "无效的访问令牌", "status": 401, "details": {"success": false, "code": 401, "message": "无效的访问令牌", "data": null, "timestamp": 1754729046605}}, {"test": "获取所有站点配置", "error": "无效的访问令牌", "status": 401, "details": {"success": false, "code": 401, "message": "无效的访问令牌", "data": null, "timestamp": 1754729046606}}, {"test": "获取公开站点配置", "error": "参数验证失败", "status": 400, "details": {"success": false, "code": 400, "message": "参数验证失败", "data": {"errors": ["语言代码必须是: en, de, ru之一"]}, "timestamp": 1754729046608}}, {"test": "获取API配置", "error": "无效的访问令牌", "status": 401, "details": {"success": false, "code": 401, "message": "无效的访问令牌", "data": null, "timestamp": 1754729046609}}, {"test": "获取激活的API配置", "error": "无效的访问令牌", "status": 401, "details": {"success": false, "code": 401, "message": "无效的访问令牌", "data": null, "timestamp": 1754729046610}}, {"test": "获取上传配置", "error": "请求的资源不存在", "status": 404, "details": {"success": false, "code": 404, "message": "请求的资源不存在", "data": {"method": "GET", "url": "/api/admin/media/upload-config", "availableRoutes": ["/health", "/api"]}, "timestamp": 1754729046612}}, {"test": "获取媒体列表", "error": "无效的访问令牌", "status": 401, "details": {"success": false, "code": 401, "message": "无效的访问令牌", "data": null, "timestamp": 1754729046613}}, {"test": "获取存储统计", "error": "请求的资源不存在", "status": 404, "details": {"success": false, "code": 404, "message": "请求的资源不存在", "data": {"method": "GET", "url": "/api/admin/media/storage-stats", "availableRoutes": ["/health", "/api"]}, "timestamp": 1754729046614}}, {"test": "获取热门文章", "error": "文章不存在", "status": 404, "details": {"success": false, "code": 404, "message": "文章不存在", "data": null, "timestamp": 1754729046800}}, {"test": "获取相关文章", "error": "请求的资源不存在", "status": 404, "details": {"success": false, "code": 404, "message": "请求的资源不存在", "data": {"method": "GET", "url": "/api/public/articles/1/related?language=zh", "availableRoutes": ["/health", "/api"]}, "timestamp": 1754729046802}}, {"test": "获取热门标签", "error": "请求的资源不存在", "status": 404, "details": {"success": false, "code": 404, "message": "请求的资源不存在", "data": {"method": "GET", "url": "/api/public/tags/popular?language=zh", "availableRoutes": ["/health", "/api"]}, "timestamp": 1754729046803}}, {"test": "获取站点统计", "error": "请求的资源不存在", "status": 404, "details": {"success": false, "code": 404, "message": "请求的资源不存在", "data": {"method": "GET", "url": "/api/public/stats?language=zh", "availableRoutes": ["/health", "/api"]}, "timestamp": 1754729046805}}, {"test": "无效参数错误", "error": "应该返回400或404状态码"}], "startTime": "2025-08-09T08:44:03.695Z", "sections": {"健康检查和基础API": {"total": 2, "passed": 2, "failed": 0, "startTime": "2025-08-09T08:44:05.053Z", "endTime": "2025-08-09T08:44:05.233Z", "duration": 180}, "认证系统": {"total": 4, "passed": 1, "failed": 3, "startTime": "2025-08-09T08:44:05.233Z", "endTime": "2025-08-09T08:44:05.836Z", "duration": 603}, "分类管理": {"total": 3, "passed": 1, "failed": 2, "startTime": "2025-08-09T08:44:05.836Z", "endTime": "2025-08-09T08:44:06.205Z", "duration": 369}, "文章管理": {"total": 4, "passed": 0, "failed": 4, "startTime": "2025-08-09T08:44:06.205Z", "endTime": "2025-08-09T08:44:06.595Z", "duration": 390}, "评论管理": {"total": 4, "passed": 0, "failed": 4, "startTime": "2025-08-09T08:44:06.595Z", "endTime": "2025-08-09T08:44:06.603Z", "duration": 8}, "翻译功能": {"total": 2, "passed": 0, "failed": 2, "startTime": "2025-08-09T08:44:06.603Z", "endTime": "2025-08-09T08:44:06.606Z", "duration": 3}, "配置管理": {"total": 4, "passed": 0, "failed": 4, "startTime": "2025-08-09T08:44:06.606Z", "endTime": "2025-08-09T08:44:06.610Z", "duration": 4}, "媒体管理": {"total": 3, "passed": 0, "failed": 3, "startTime": "2025-08-09T08:44:06.611Z", "endTime": "2025-08-09T08:44:06.615Z", "duration": 4}, "公开API": {"total": 4, "passed": 0, "failed": 4, "startTime": "2025-08-09T08:44:06.615Z", "endTime": "2025-08-09T08:44:06.805Z", "duration": 190}, "错误处理": {"total": 3, "passed": 2, "failed": 1, "startTime": "2025-08-09T08:44:06.805Z", "endTime": "2025-08-09T08:44:06.810Z", "duration": 5}}, "endTime": "2025-08-09T08:44:06.810Z", "duration": 3115}