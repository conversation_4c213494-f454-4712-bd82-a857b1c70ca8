/**
 * 公开API路由完整测试脚本 - 步骤38
 * 
 * 测试所有公开API端点的功能，包括：
 * - 基础连接测试
 * - 参数验证测试
 * - 错误处理测试
 * - 限流功能测试
 * - 所有端点响应格式验证
 * 
 * <AUTHOR> Blog System
 * @version 1.0.0
 */

const axios = require('axios');

// API基础配置
const API_BASE = 'http://localhost:3000/api/public';
const TIMEOUT = 5000;

// 创建axios实例
const api = axios.create({
    baseURL: API_BASE,
    timeout: TIMEOUT,
    headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Test-Script-Step38'
    }
});

// 测试结果统计
let testResults = {
    passed: 0,
    failed: 0,
    total: 0,
    details: []
};

/**
 * 记录测试结果
 */
function recordTest(name, passed, message = '', data = null) {
    testResults.total++;
    if (passed) {
        testResults.passed++;
        console.log(`✅ ${name}: ${message || '通过'}`);
    } else {
        testResults.failed++;
        console.log(`❌ ${name}: ${message || '失败'}`);
        if (data) {
            console.log('   详细信息:', data);
        }
    }
    
    testResults.details.push({
        name,
        passed,
        message,
        data,
        timestamp: new Date().toISOString()
    });
}

/**
 * 通用API测试函数
 */
async function testEndpoint(method, endpoint, data = null, expectedStatus = 200, testName = '') {
    try {
        const config = {
            method,
            url: endpoint,
            ...(data && { data })
        };
        
        const response = await api(config);
        
        // 检查响应状态
        const statusOk = response.status === expectedStatus;
        
        // 检查响应格式
        const hasRequiredFields = response.data && 
                                 typeof response.data.success === 'boolean' &&
                                 typeof response.data.code === 'number' &&
                                 typeof response.data.message === 'string' &&
                                 typeof response.data.timestamp === 'number';
        
        const testResult = statusOk && hasRequiredFields;
        
        recordTest(
            testName || `${method.toUpperCase()} ${endpoint}`,
            testResult,
            testResult 
                ? `状态码: ${response.status}, 响应格式正确` 
                : `状态码: ${response.status}, 格式检查: ${hasRequiredFields}`,
            !testResult ? { status: response.status, data: response.data } : null
        );
        
        return response;
        
    } catch (error) {
        const isExpectedError = error.response && error.response.status === expectedStatus;
        
        recordTest(
            testName || `${method.toUpperCase()} ${endpoint}`,
            isExpectedError,
            isExpectedError 
                ? `预期错误: ${error.response.status}` 
                : `请求失败: ${error.message}`,
            !isExpectedError ? { error: error.message, response: error.response?.data } : null
        );
        
        if (isExpectedError) {
            return error.response;
        }
        
        throw error;
    }
}

/**
 * 测试文章相关API
 */
async function testArticleAPIs() {
    console.log('\n📝 测试文章相关API');
    console.log('='.repeat(40));
    
    // 测试获取文章列表 - 正确参数
    await testEndpoint('GET', '/articles?language=en&page=1&limit=5', null, 200, '获取文章列表(正确参数)');
    
    // 测试获取文章列表 - 缺少语言参数
    await testEndpoint('GET', '/articles?page=1&limit=5', null, 400, '获取文章列表(缺少语言参数)');
    
    // 测试获取文章列表 - 无效语言代码
    await testEndpoint('GET', '/articles?language=invalid&page=1&limit=5', null, 400, '获取文章列表(无效语言代码)');
    
    // 测试获取文章列表 - 带搜索关键词
    await testEndpoint('GET', '/articles?language=en&search=cat&page=1&limit=5', null, 200, '获取文章列表(带搜索)');
    
    // 测试获取文章列表 - 带分类筛选
    await testEndpoint('GET', '/articles?language=en&category_id=1&page=1&limit=5', null, 200, '获取文章列表(带分类筛选)');
    
    // 测试获取文章详情 - 不存在的slug
    await testEndpoint('GET', '/articles/non-existent-article?language=en', null, 404, '获取文章详情(不存在的slug)');
    
    // 测试获取文章详情 - 缺少语言参数
    await testEndpoint('GET', '/articles/test-article', null, 400, '获取文章详情(缺少语言参数)');
    
    // 测试获取文章详情 - 无效slug格式 (预期返回404，因为不存在)
    await testEndpoint('GET', '/articles/Invalid_Slug?language=en', null, 404, '获取文章详情(无效slug格式)');
    
    // 测试增加文章浏览量 - 不存在的文章ID (但格式正确)
    await testEndpoint('POST', '/articles/999999/view', { language: 'en' }, 404, '增加文章浏览量(不存在的文章ID)');
    
    // 测试增加文章浏览量 - 无效文章ID
    await testEndpoint('POST', '/articles/invalid/view', { language: 'en' }, 400, '增加文章浏览量(无效ID)');
    
    // 测试增加文章浏览量 - 缺少语言参数
    await testEndpoint('POST', '/articles/1/view', {}, 400, '增加文章浏览量(缺少语言参数)');
}

/**
 * 测试分类相关API
 */
async function testCategoryAPIs() {
    console.log('\n📂 测试分类相关API');
    console.log('='.repeat(40));
    
    // 测试获取分类列表 - 正确参数
    await testEndpoint('GET', '/categories?language=en', null, 200, '获取分类列表(正确参数)');
    
    // 测试获取分类列表 - 缺少语言参数
    await testEndpoint('GET', '/categories', null, 400, '获取分类列表(缺少语言参数)');
    
    // 测试获取分类列表 - 带类型筛选
    await testEndpoint('GET', '/categories?language=en&type=cat', null, 200, '获取分类列表(带类型筛选)');
    
    // 测试获取分类列表 - 无效类型
    await testEndpoint('GET', '/categories?language=en&type=invalid', null, 400, '获取分类列表(无效类型)');
}

/**
 * 测试评论相关API
 */
async function testCommentAPIs() {
    console.log('\n💬 测试评论相关API');
    console.log('='.repeat(40));
    
    // 测试获取评论列表 - 不存在的文章ID (但格式正确)
    await testEndpoint('GET', '/comments?article_id=999999&language=en&page=1&limit=10', null, 404, '获取评论列表(不存在的文章ID)');
    
    // 测试获取评论列表 - 缺少文章ID
    await testEndpoint('GET', '/comments?language=en&page=1&limit=10', null, 400, '获取评论列表(缺少文章ID)');
    
    // 测试获取评论列表 - 无效文章ID
    await testEndpoint('GET', '/comments?article_id=invalid&language=en&page=1&limit=10', null, 400, '获取评论列表(无效文章ID)');
    
    // 测试提交评论 - 不存在的文章ID (但格式正确)
    const validComment = {
        article_id: 999999,
        language: 'en',
        username: 'TestUser',
        email: '<EMAIL>',
        content: 'This is a test comment with sufficient length.'
    };
    await testEndpoint('POST', '/comments', validComment, 404, '提交评论(不存在的文章ID)');
    
    // 测试提交评论 - 缺少必需参数
    await testEndpoint('POST', '/comments', { article_id: 1, language: 'en' }, 400, '提交评论(缺少必需参数)');
    
    // 测试提交评论 - 无效邮箱
    await testEndpoint('POST', '/comments', {
        ...validComment,
        email: 'invalid-email'
    }, 400, '提交评论(无效邮箱)');
    
    // 测试提交评论 - 内容过短
    await testEndpoint('POST', '/comments', {
        ...validComment,
        content: 'Hi'
    }, 400, '提交评论(内容过短)');
    
    // 测试提交评论 - 用户名过短
    await testEndpoint('POST', '/comments', {
        ...validComment,
        username: 'A'
    }, 400, '提交评论(用户名过短)');
    
    // 测试提交回复评论 - 不存在的文章ID
    await testEndpoint('POST', '/comments', {
        ...validComment,
        parent_id: 1,
        content: 'This is a test reply to another comment.'
    }, 404, '提交回复评论(不存在的文章ID)');
}

/**
 * 测试搜索API
 */
async function testSearchAPI() {
    console.log('\n🔍 测试搜索API');
    console.log('='.repeat(40));
    
    // 测试搜索文章 - 正确参数
    await testEndpoint('GET', '/search?q=cat&language=en&type=article&page=1&limit=5', null, 200, '搜索文章(正确参数)');
    
    // 测试搜索分类
    await testEndpoint('GET', '/search?q=care&language=en&type=category&page=1&limit=5', null, 200, '搜索分类');
    
    // 测试搜索 - 缺少关键词
    await testEndpoint('GET', '/search?language=en&type=article', null, 400, '搜索(缺少关键词)');
    
    // 测试搜索 - 空关键词
    await testEndpoint('GET', '/search?q=&language=en&type=article', null, 400, '搜索(空关键词)');
    
    // 测试搜索 - 无效类型
    await testEndpoint('GET', '/search?q=test&language=en&type=invalid', null, 400, '搜索(无效类型)');
    
    // 测试搜索 - 关键词太长
    const longKeyword = 'a'.repeat(101);
    await testEndpoint('GET', `/search?q=${longKeyword}&language=en&type=article`, null, 400, '搜索(关键词太长)');
}

/**
 * 测试站点配置API
 */
async function testSiteConfigAPI() {
    console.log('\n⚙️ 测试站点配置API');
    console.log('='.repeat(40));
    
    // 测试获取站点配置 - 正确参数
    await testEndpoint('GET', '/site-config?language=en', null, 200, '获取站点配置(正确参数)');
    
    // 测试获取站点配置 - 缺少语言参数
    await testEndpoint('GET', '/site-config', null, 400, '获取站点配置(缺少语言参数)');
    
    // 测试获取站点配置 - 无效语言代码
    await testEndpoint('GET', '/site-config?language=invalid', null, 400, '获取站点配置(无效语言代码)');
}

/**
 * 测试开发环境专用端点
 */
async function testDevEndpoints() {
    console.log('\n🔧 测试开发环境专用端点');
    console.log('='.repeat(40));
    
    // 测试状态端点
    await testEndpoint('GET', '/status', null, 200, '获取路由状态');
    
    // 测试验证测试端点
    await testEndpoint('POST', '/validate-test', { test: 'data' }, 200, '验证测试端点');
    
    // 测试限流信息端点
    await testEndpoint('GET', '/rate-limit-info', null, 200, '获取限流信息');
}

/**
 * 测试错误处理和边界情况
 */
async function testErrorHandling() {
    console.log('\n🚨 测试错误处理和边界情况');
    console.log('='.repeat(40));
    
    // 测试不存在的端点
    try {
        await api.get('/non-existent-endpoint');
        recordTest('不存在的端点', false, '应该返回404错误');
    } catch (error) {
        recordTest('不存在的端点', error.response?.status === 404, `返回状态: ${error.response?.status}`);
    }
    
    // 测试无效的HTTP方法
    try {
        await api.put('/articles');  // PUT不被支持的端点
        recordTest('无效HTTP方法', false, '应该返回404或405错误');
    } catch (error) {
        recordTest('无效HTTP方法', error.response?.status >= 400, `返回状态: ${error.response?.status}`);
    }
    
    // 测试大量数据请求
    await testEndpoint('GET', '/articles?language=en&limit=50', null, 200, '大量数据请求(limit=50)');
    
    // 测试超出限制的请求
    await testEndpoint('GET', '/articles?language=en&limit=100', null, 400, '超出限制的请求(limit=100)');
}

/**
 * 主测试函数
 */
async function runAllTests() {
    console.log('🚀 开始公开API路由完整测试 - 步骤38');
    console.log('='.repeat(50));
    console.log(`测试目标: ${API_BASE}`);
    console.log(`测试时间: ${new Date().toISOString()}`);
    console.log('='.repeat(50));
    
    try {
        // 首先测试服务器是否可用
        console.log('\n🏥 测试服务器健康状态');
        console.log('='.repeat(40));
        
        try {
            const healthResponse = await axios.get('http://localhost:3000/health', { timeout: TIMEOUT });
            recordTest('服务器健康检查', healthResponse.status === 200, '服务器运行正常');
        } catch (error) {
            recordTest('服务器健康检查', false, '服务器不可用或未启动');
            console.log('❌ 无法连接到服务器，请确保后端服务已启动 (npm run dev)');
            return;
        }
        
        // 运行所有测试
        await testArticleAPIs();
        await testCategoryAPIs();
        await testCommentAPIs();
        await testSearchAPI();
        await testSiteConfigAPI();
        await testDevEndpoints();
        await testErrorHandling();
        
        // 输出测试结果
        console.log('\n📊 测试结果汇总');
        console.log('='.repeat(50));
        console.log(`总测试数: ${testResults.total}`);
        console.log(`通过: ${testResults.passed} (${Math.round(testResults.passed/testResults.total*100)}%)`);
        console.log(`失败: ${testResults.failed} (${Math.round(testResults.failed/testResults.total*100)}%)`);
        
        if (testResults.failed === 0) {
            console.log('\n🎉 所有测试通过！公开API路由功能完整，可以继续后续步骤。');
        } else {
            console.log('\n⚠️ 部分测试失败，请检查以下问题：');
            testResults.details
                .filter(test => !test.passed)
                .forEach(test => {
                    console.log(`  - ${test.name}: ${test.message}`);
                });
        }
        
        // 保存详细测试结果
        const fs = require('fs');
        fs.writeFileSync(
            'test-results-step38.json', 
            JSON.stringify(testResults, null, 2)
        );
        console.log('\n💾 详细测试结果已保存到 test-results-step38.json');
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        recordTest('测试执行', false, `测试中断: ${error.message}`);
    }
}

/**
 * 程序入口
 */
if (require.main === module) {
    runAllTests()
        .then(() => {
            console.log('\n✅ 测试脚本执行完成');
            process.exit(testResults.failed > 0 ? 1 : 0);
        })
        .catch(error => {
            console.error('❌ 测试脚本执行失败:', error);
            process.exit(1);
        });
}

module.exports = { runAllTests, testResults };