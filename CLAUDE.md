# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🚀 快速开始指南

### 如果你要继续开发这个项目：

1. **查看当前进度**：步骤1-40已完成（后端100%），步骤41开始是React管理后台
2. **启动后端服务**：
   ```bash
   cd /Volumes/Bengtai/seo/zhanqun188/pet-blog-system/backend
   npm run dev  # 端口3000
   ```
3. **测试API可用性**：
   ```bash
   curl http://localhost:3000/health
   ```
4. **开始下一步开发**（步骤41）：
   ```bash
   cd ../admin
   npx create-react-app . --template typescript
   ```

### 如果你要修复或优化现有代码：

1. **所有后端代码**在：`pet-blog-system/backend/src/`
2. **测试脚本**在：`pet-blog-system/backend/test-*.js`
3. **环境变量**在：`pet-blog-system/backend/.env`（包含敏感信息）
4. **开发文档**在：`docs/` 目录

## 🎯 项目当前状态

**开发进度**: 44.4% (40/90步骤完成) | **后端API**: ✅ 100%完成 | **管理后台**: ⏳ 待开始 | **前端站点**: ⏳ 待开始

### 已完成里程碑
- ✅ 后端API系统（步骤1-40，2025-08-09完成）
- ✅ 数据库架构（11个表，4种语言支持）
- ✅ AI翻译服务集成（Gemini API）
- ✅ 完整的RESTful API（公开API + 管理API）
- ✅ JWT认证和授权系统
- ✅ 文件上传和媒体管理
- ✅ 分级限流和安全防护

### 下一步任务（立即可执行）
```bash
# 步骤41：初始化React管理后台
cd /Volumes/Bengtai/seo/zhanqun188/pet-blog-system/admin
npx create-react-app . --template typescript
npm install react-router-dom@6 axios@1.7.0 @types/react-router-dom
```

## 项目概述

这是一个多语言宠物博客站群系统，专注于猫狗宠物知识分享。系统设计核心是**让每个语言站点在前端表现上完全独立**，避免被搜索引擎识别为站群，同时后台统一管理。

### 核心架构特点
- **前后端分离**：后端统一API（已完成），前端多个独立站点（待开发）
- **多语言独立部署**：每个语言版本独立域名、独立前端，避免站群检测
- **静态站点生成**：使用Astro生成静态HTML，SEO友好
- **AI翻译集成**：支持一键AI翻译（已实现），人工校对
- **统一管理后台**：React管理后台统一管理所有语言内容（待开发）

## 技术栈

- **后端**: Node.js 20.x + Express 4.x + MySQL 9.0.1
- **管理后台**: React + TypeScript
- **前端**: Astro 4.x (静态站点生成器)
- **部署**: Linux + Nginx + PM2 + 宝塔面板

## 项目结构

```
zhanqun188/
├── docs/                    # 完整的开发文档（90步骤开发指南）
│   ├── 00-项目总览.md      # 项目整体介绍
│   ├── 01-需求规格.md      # 功能需求说明
│   ├── 02-技术架构.md      # 技术实现方案
│   ├── 03-数据库设计.md    # 数据表结构（含敏感信息需移至环境变量）
│   ├── 04-API设计.md       # RESTful API接口文档
│   ├── 05-前端设计.md      # UI/UX设计规范
│   ├── 06-SEO策略.md       # SEO优化策略
│   ├── 07-部署方案.md      # 服务器部署方案
│   └── 08-开发步骤.md      # 核心：90个独立开发步骤
└── pet-blog-system/         # 实际代码目录（待创建）
    ├── backend/             # 后端API服务
    ├── admin/              # React管理后台
    ├── frontend/           # 前端站点
    │   ├── en/            # 英语站点（Astro）
    │   ├── de/            # 德语站点（Astro）
    │   └── ru/            # 俄语站点（Astro）
    └── uploads/           # 上传文件存储

```

## 常用开发命令

### 后端开发（✅ 已完成配置）
```bash
cd /Volumes/Bengtai/seo/zhanqun188/pet-blog-system/backend

# 启动开发服务器（带热重载）
npm run dev                # 使用nodemon，端口3000

# 生产模式运行
npm start                  # 使用node server.js

# 数据库操作
npm run setup-db          # 初始化数据库表结构
npm run test-db           # 测试数据库连接

# 代码质量
npm run lint              # ESLint代码检查
npm test                  # Jest单元测试（需配置）

# 测试脚本（已创建的测试文件）
node test-all-api-step40.js        # 综合API测试
node test-auth-api.js               # 认证API测试
node test-article-routes.js         # 文章路由测试
node test-translation-service.js    # 翻译服务测试
node verify-structure.js            # 验证项目结构
```

### 管理后台开发
```bash
cd admin
npx create-react-app . --template typescript
npm install react-router-dom axios

# 开发模式
npm start

# 构建生产版本
npm run build
```

### 前端开发（Astro）
```bash
cd frontend/en
npm create astro@latest . -- --template minimal
npm install @astrojs/sitemap @astrojs/robots-txt tailwindcss

# 开发模式
npm run dev

# 构建静态站点
npm run build
```

### 数据库操作
```bash
# 连接远程MySQL数据库
mysql -h ************ -u bengtai -p
# 密码: weizhen258
```

## API端点列表（✅ 已实现）

### 系统端点
- `GET /health` - 健康检查（包含数据库状态）
- `GET /api` - API基础信息和功能列表

### 认证API（/api/admin/auth）
- `POST /api/admin/auth/login` - 管理员登录（返回JWT）
- `POST /api/admin/auth/refresh` - 刷新Token
- `POST /api/admin/auth/logout` - 退出登录

### 公开API（/api/public）- 无需认证
- `GET /api/public/articles` - 获取文章列表（分页、搜索、语言筛选）
- `GET /api/public/articles/:id` - 获取文章详情
- `GET /api/public/categories` - 获取分类列表（支持树形结构）
- `GET /api/public/comments/article/:articleId` - 获取文章评论
- `POST /api/public/comments` - 提交评论（需审核）
- `GET /api/public/config/:language` - 获取站点配置

### 管理API（/api/admin）- 需要JWT认证

#### 文章管理
- `GET /api/admin/articles` - 文章列表（管理视图）
- `GET /api/admin/articles/:id` - 文章详情
- `POST /api/admin/articles` - 创建文章
- `PUT /api/admin/articles/:id` - 更新文章
- `DELETE /api/admin/articles/:id` - 删除文章
- `POST /api/admin/articles/:id/publish` - 发布文章
- `POST /api/admin/articles/:id/unpublish` - 取消发布
- `POST /api/admin/articles/:id/translate` - AI翻译文章
- `POST /api/admin/articles/translate-batch` - 批量翻译

#### 分类管理
- `GET /api/admin/categories` - 分类列表
- `POST /api/admin/categories` - 创建分类
- `PUT /api/admin/categories/:id` - 更新分类
- `DELETE /api/admin/categories/:id` - 删除分类

#### 评论管理
- `GET /api/admin/comments` - 评论列表
- `PUT /api/admin/comments/:id/approve` - 批准评论
- `PUT /api/admin/comments/:id/reject` - 拒绝评论
- `DELETE /api/admin/comments/:id` - 删除评论
- `POST /api/admin/comments/batch-approve` - 批量批准
- `POST /api/admin/comments/batch-delete` - 批量删除

#### 媒体管理
- `POST /api/admin/media/upload` - 上传文件（支持图片压缩）
- `GET /api/admin/media` - 媒体列表
- `DELETE /api/admin/media/:id` - 删除媒体

#### 配置管理
- `GET /api/admin/configs` - 获取所有配置
- `PUT /api/admin/configs/:id` - 更新配置
- `POST /api/admin/configs` - 创建配置

## 核心开发流程

### 内容发布流程（已实现后端部分）
1. 管理员用中文撰写文章 ✅
2. 上传到后台管理系统 ✅（API已完成）
3. 调用AI翻译API（Gemini）进行多语言翻译 ✅
4. 翻译结果保存为草稿 ✅
5. 人工校对和修改 ✅（API支持）
6. 选择发布的语言站点 ✅（多语言字段）
7. Astro构建静态页面 ⏳（待开发）
8. 前端显示 ⏳（待开发）

### API设计原则（已实现）
- ✅ RESTful风格
- ✅ 统一响应格式：`{success, code, message, data, timestamp}`
- ✅ JWT认证（管理API）
- ✅ 公开API无需认证（前端调用）
- ✅ 限流策略：
  - 公开API: 100次/分钟
  - 管理API: 1000次/分钟
  - 认证API: 5次/15分钟
  - 上传API: 10次/分钟

### 多语言架构设计
- **域名映射**：通过Nginx识别不同域名，路由到不同语言前端
- **数据隔离**：评论、配置等按语言独立存储（已实现）
- **URL本地化**：每种语言完全本地化的URL结构
- **独立部署**：每个语言版本可独立部署到不同服务器

## 重要配置

### 环境变量（需要创建.env文件）
```env
# 数据库配置（敏感信息，生产环境需更改）
DB_HOST=************
DB_USER=bengtai
DB_PASSWORD=weizhen258
DB_NAME=bengtai

# AI翻译API（敏感信息，生产环境需更改）
OPENAI_API_URL=https://ai.wanderintree.top
OPENAI_API_KEY=sk-SMXjycC5GnJRswpJB8Ef6f632d794bBa9a1bAbB828E7Ee9d
OPENAI_MODEL=gemini-2.5-pro

# JWT配置
JWT_SECRET=your-secret-key-here
JWT_EXPIRE=24h
```

### Nginx多域名配置示例
```nginx
# 英语站点
server {
    server_name example.com;
    root /www/wwwroot/pet-blog/frontend-en/dist;
    location /api/ {
        proxy_pass http://localhost:3000/api/;
    }
}

# 德语站点
server {
    server_name example.de;
    root /www/wwwroot/pet-blog/frontend-de/dist;
    location /api/ {
        proxy_pass http://localhost:3000/api/;
    }
}
```

## SEO优化重点

1. **避免站群检测**
   - 每个语言站点独立IP段
   - 不进行站点间交叉链接
   - 内容通过AI翻译确保独特性
   - 独立品牌标识

2. **技术SEO**
   - 静态HTML生成（Astro）
   - 结构化数据（Schema.org）
   - Core Web Vitals优化（LCP<2.5s, FID<100ms, CLS<0.1）
   - XML Sitemap + robots.txt
   - hreflang标签（多语言）

3. **内容SEO**
   - URL完全本地化
   - Meta标签优化（标题60字符，描述160字符）
   - 图片Alt文本
   - 内部链接优化（3-5个/文章）

## 开发注意事项

1. **安全性**
   - 所有敏感信息必须使用环境变量
   - SQL注入防护（使用参数化查询）
   - XSS防护（sanitize-html）
   - CSRF防护
   - JWT认证
   - 速率限制

2. **性能优化**
   - 图片懒加载 + WebP格式
   - 静态资源CDN
   - Gzip/Brotli压缩
   - 数据库查询优化（正确索引）
   - Redis缓存（可选）

3. **代码规范**
   - 模块化设计
   - 统一错误处理
   - 完整的注释
   - Git版本控制

## 测试要点

1. **功能测试**
   - API端点测试（Postman）
   - 翻译流程测试
   - 评论系统测试
   - 管理后台功能测试

2. **性能测试**
   - Lighthouse评分
   - 并发访问测试（目标：1万日访问量）
   - 页面加载速度（<3秒）

3. **SEO测试**
   - 结构化数据验证
   - Meta标签检查
   - Sitemap验证
   - Mobile-friendly测试

## 部署清单

1. 服务器要求：2核4G内存，50G SSD，10Mbps带宽
2. 软件环境：Ubuntu 20.04, Nginx, Node.js 20.x, PM2
3. SSL证书配置（所有域名）
4. 防火墙规则（22, 80, 443端口）
5. 备份策略（每日自动备份）
6. 监控配置（日志、性能、健康检查）

## 开发步骤指南

项目包含90个独立开发步骤，详见 `docs/08-开发步骤.md`。主要阶段：

1. **环境准备**（步骤1-10）：项目初始化、数据库创建
2. **后端基础**（步骤11-25）：Express设置、认证系统
3. **后端核心**（步骤26-40）：API开发、翻译集成
4. **管理后台**（步骤41-50）：React后台开发
5. **前端开发**（步骤51-65）：Astro多语言站点
6. **集成测试**（步骤66-75）：全面测试
7. **部署上线**（步骤76-85）：服务器部署
8. **优化文档**（步骤86-90）：性能优化、文档编写

每个步骤都是独立任务，可在新的Claude对话中执行。执行时提供步骤编号和相关参考文档即可。

## API测试方法

### 使用curl测试API
```bash
# 健康检查
curl http://localhost:3000/health

# 管理员登录（获取Token）
curl -X POST http://localhost:3000/api/admin/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 使用Token调用管理API
TOKEN="your-jwt-token-here"
curl http://localhost:3000/api/admin/articles \
  -H "Authorization: Bearer $TOKEN"

# 获取公开文章列表（无需认证）
curl "http://localhost:3000/api/public/articles?language=zh&page=1&limit=10"

# 测试AI翻译
curl -X POST http://localhost:3000/api/admin/articles/1/translate \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"targetLanguages":["en","de","ru"]}'
```

### 使用测试脚本
```bash
cd backend

# 运行综合测试（测试所有API）
node test-all-api-step40.js

# 单独测试模块
node test-auth-api.js            # 测试认证
node test-article-routes.js      # 测试文章API
node test-translation-service.js # 测试翻译服务
node test-media-api.js           # 测试文件上传
```

## 故障排查

### 常见问题及解决方案

#### 1. 数据库连接失败
```bash
# 检查连接
node database/test-connection.js

# 可能原因：
# - 防火墙阻止了3306端口
# - MySQL未开启远程访问
# - 用户权限不足
# - .env文件配置错误

# 解决方案：
# 1. 检查.env文件中的数据库配置
# 2. 确保MySQL允许远程连接
# 3. 验证用户权限：GRANT ALL PRIVILEGES
```

#### 2. API跨域问题
```javascript
// 检查CORS配置（src/middleware/cors.js）
// 确保允许的域名包含前端域名
const allowedOrigins = [
  'http://localhost:3001',  // 管理后台
  'http://localhost:4321',  // Astro前端
  // 添加生产环境域名
];
```

#### 3. JWT认证失败
```bash
# 检查Token格式
# Authorization: Bearer <token>

# 常见错误：
# - Token过期（默认24小时）
# - Token格式错误（缺少Bearer前缀）
# - JWT_SECRET未配置

# 刷新Token
curl -X POST http://localhost:3000/api/admin/auth/refresh \
  -H "Authorization: Bearer $OLD_TOKEN"
```

#### 4. 文件上传失败
```bash
# 检查uploads目录权限
ls -la uploads/

# 确保目录可写
chmod -R 755 uploads/

# 检查文件大小限制（默认10MB）
# 修改src/index.js中的限制：
# app.use(express.json({ limit: '10mb' }));
```

#### 5. AI翻译API错误
```bash
# 测试AI API连接
node test-ai-api.js

# 常见问题：
# - API密钥无效
# - 请求频率超限
# - 网络连接问题

# 检查.env配置：
# OPENAI_API_URL=https://ai.wanderintree.top
# OPENAI_API_KEY=your-key-here
# OPENAI_MODEL=gemini-2.5-pro
```

#### 6. 端口被占用
```bash
# 查找占用3000端口的进程
lsof -i :3000

# 终止进程
kill -9 <PID>

# 或修改端口（.env文件）
PORT=3001
```

#### 7. Node版本问题
```bash
# 项目需要Node.js 20.x或更高
node -v

# 使用nvm切换版本
nvm use 20
```

## 数据库架构（已实现）

### 核心数据表（11个表）
```sql
-- 1. 管理员表（admins）
-- 2. 文章表（articles）- 支持多语言字段
-- 3. 分类表（categories）- 支持多语言和层级
-- 4. 评论表（comments）- 按语言隔离
-- 5. 文章分类关联表（article_categories）
-- 6. 翻译记录表（translations）
-- 7. 媒体表（media）
-- 8. 配置表（configs）- 站点配置
-- 9. SEO元数据表（seo_metadata）
-- 10. 标签表（tags）
-- 11. 文章标签关联表（article_tags）
```

### 支持的语言
- `zh` - 中文（原始内容）
- `en` - 英语
- `de` - 德语
- `ru` - 俄语

### 数据库特性
- 使用InnoDB引擎（支持事务）
- UTF8MB4字符集（支持emoji）
- 正确的索引设计（查询优化）
- 外键约束（数据完整性）

## 架构决策和最佳实践

### 1. 安全性设计
- **密码加密**：使用bcrypt（10轮salt）
- **JWT认证**：无状态认证，24小时过期
- **SQL注入防护**：参数化查询
- **XSS防护**：使用sanitize-html
- **CSRF防护**：Token验证
- **限流保护**：分级限流策略
- **Helmet集成**：安全HTTP头

### 2. 性能优化
- **数据库连接池**：最大10个连接
- **响应压缩**：Gzip压缩（level 6）
- **静态资源缓存**：生产环境7天
- **图片处理**：Sharp自动压缩和缩略图
- **分页查询**：默认20条/页
- **索引优化**：关键字段都有索引

### 3. 代码组织
```
backend/src/
├── config/         # 配置文件（数据库等）
├── controllers/    # 业务逻辑控制器
├── middleware/     # Express中间件
├── models/        # 数据模型
├── routes/        # API路由定义
├── services/      # 业务服务（翻译、上传等）
├── utils/         # 工具函数
└── index.js       # 应用入口
```

### 4. 错误处理
- 统一错误格式
- 分层错误处理（控制器→中间件→全局）
- 详细的错误日志
- 用户友好的错误消息

### 5. 测试策略
- 单元测试（models, utils）
- 集成测试（API端点）
- 端到端测试（完整流程）
- 性能测试（负载测试）

## 扩展指南

### 添加新语言站点
```bash
# 1. 在数据库中添加语言配置
INSERT INTO configs (config_key, config_value, language) 
VALUES ('site_name', 'Pet Blog Russia', 'ru');

# 2. 创建前端目录
mkdir -p frontend/ru
cd frontend/ru
npm create astro@latest . -- --template minimal

# 3. 更新CORS配置（backend/src/middleware/cors.js）
const allowedOrigins = [
  'https://ru.yourdomain.com',
  // ... 其他域名
];

# 4. 配置Nginx
server {
    server_name ru.yourdomain.com;
    root /www/wwwroot/pet-blog/frontend-ru/dist;
    location /api/ {
        proxy_pass http://localhost:3000/api/;
    }
}

# 5. 更新AI翻译支持的语言列表
```

### 添加新功能模块
1. 创建数据模型（`models/`）
2. 创建控制器（`controllers/`）
3. 定义路由（`routes/`）
4. 添加中间件（如需要）
5. 更新API文档
6. 编写测试用例

## 项目维护建议

### 日常维护
- 定期备份数据库（每日）
- 监控API性能（响应时间）
- 检查错误日志
- 更新依赖包（安全补丁）

### 性能监控
- API响应时间 < 200ms
- 数据库查询时间 < 50ms
- 内存使用 < 500MB
- CPU使用 < 50%

### 安全检查
- 定期更新依赖（npm audit）
- 检查SQL注入漏洞
- 验证认证机制
- 审查日志文件